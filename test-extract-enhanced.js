const fetch = require('node-fetch');

async function testEnhancedExtract() {
  try {
    console.log('🚀 بدء اختبار استخراج Schema المحسن مع وصف الأعمدة...');
    
    const response = await fetch('http://localhost:3000/api/database/extract', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'mssql',
        host: 'localhost',
        port: 1433,
        username: 'myuser',
        password: 'Aa227520',
        database: 'SalesTempDB',
        useWindowsAuth: false
      })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ نجح استخراج Schema المحسن!');
      console.log(`📊 عدد الجداول: ${result.tablesCount}`);
      console.log('🔍 جاري فحص وصف الأعمدة...');
    } else {
      console.error('❌ فشل استخراج Schema:', result.error);
    }
    
  } catch (error) {
    console.error('💥 خطأ في الاختبار:', error.message);
  }
}

testEnhancedExtract();
