"use client"

import React, { useState } from 'react';
import { HelpCircle, X, User, Key, Database, Shield, AlertCircle } from 'lucide-react';

interface SqlServerHelpProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SqlServerHelp({ isOpen, onClose }: SqlServerHelpProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <HelpCircle className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">
              مساعدة الاتصال بـ SQL Server
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* حل سريع للمشكلة الحالية */}
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <AlertCircle className="w-5 h-5 text-orange-600" />
              <h3 className="font-semibold text-orange-800">حل سريع لمشكلة AHMED\SQLEXPRESS</h3>
            </div>
            <div className="text-orange-700 text-sm space-y-2">
              <p><strong>المشكلة:</strong> SQL Server يعمل لكن لا يقبل اتصالات خارجية</p>
              <p><strong>الحل السريع:</strong></p>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>اضغط <kbd className="bg-gray-200 px-1 rounded">Win + R</kbd> واكتب <code>services.msc</code></li>
                <li>ابحث عن "SQL Server Browser" وتأكد أنه يعمل (Running)</li>
                <li>ابحث عن "SQL Server (SQLEXPRESS)" وتأكد أنه يعمل</li>
                <li>افتح SQL Server Configuration Manager</li>
                <li>فعّل TCP/IP كما هو موضح أدناه</li>
              </ol>
            </div>
          </div>
          {/* Windows Authentication */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <Shield className="w-5 h-5 text-green-600" />
              <h3 className="font-semibold text-green-800">Windows Authentication (الأسهل)</h3>
            </div>
            <p className="text-green-700 text-sm mb-2">
              هذا هو الخيار الأفضل إذا كان متاحاً. سيستخدم بيانات المستخدم الحالي.
            </p>
            <ul className="text-green-700 text-sm space-y-1 list-disc list-inside">
              <li>لا يتطلب اسم مستخدم أو كلمة مرور</li>
              <li>أكثر أماناً</li>
              <li>يعمل إذا كان SQL Server على نفس الشبكة</li>
            </ul>
          </div>

          {/* SQL Server Authentication */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <User className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-blue-800">SQL Server Authentication</h3>
            </div>
            <p className="text-blue-700 text-sm mb-3">
              إذا لم يعمل Windows Authentication، ستحتاج لاسم مستخدم وكلمة مرور.
            </p>
            
            <div className="space-y-3">
              <div>
                <h4 className="font-medium text-blue-800 mb-1">كيفية إيجاد اسم المستخدم:</h4>
                <ul className="text-blue-700 text-sm space-y-1 list-disc list-inside">
                  <li>افتح SQL Server Management Studio (SSMS)</li>
                  <li>اتصل بالخادم</li>
                  <li>انتقل إلى Security → Logins</li>
                  <li>ستجد قائمة بأسماء المستخدمين</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-blue-800 mb-1">أسماء مستخدمين شائعة:</h4>
                <ul className="text-blue-700 text-sm space-y-1 list-disc list-inside">
                  <li><code className="bg-blue-100 px-1 rounded">sa</code> (System Administrator)</li>
                  <li><code className="bg-blue-100 px-1 rounded">admin</code></li>
                  <li><code className="bg-blue-100 px-1 rounded">sqluser</code></li>
                  <li>اسم المستخدم الخاص بك في Windows</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Database Names */}
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <Database className="w-5 h-5 text-purple-600" />
              <h3 className="font-semibold text-purple-800">أسماء قواعد البيانات</h3>
            </div>
            <p className="text-purple-700 text-sm mb-2">
              بعد إدخال بيانات الخادم، اضغط على "عرض قواعد البيانات" لرؤية القائمة الكاملة.
            </p>
            <p className="text-purple-700 text-sm">
              أو يمكنك إدخال اسم قاعدة البيانات مباشرة إذا كنت تعرفه.
            </p>
          </div>

          {/* Common Issues */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <Key className="w-5 h-5 text-red-600" />
              <h3 className="font-semibold text-red-800">مشاكل شائعة وحلولها</h3>
            </div>
            <div className="space-y-3 text-red-700 text-sm">
              <div>
                <strong>❌ Failed to connect to [server] in 15000ms (TIMEOUT):</strong>
                <ul className="mt-1 list-disc list-inside space-y-1">
                  <li>تأكد من أن SQL Server يعمل ويقبل الاتصالات</li>
                  <li>فعّل TCP/IP في SQL Server Configuration Manager</li>
                  <li>تأكد من أن SQL Server Browser يعمل</li>
                  <li>تحقق من إعدادات الجدار الناري (Windows Firewall)</li>
                </ul>
              </div>
              <div>
                <strong>❌ خطأ في المصادقة:</strong>
                <ul className="mt-1 list-disc list-inside space-y-1">
                  <li>تأكد من أن SQL Server Authentication مفعل</li>
                  <li>تحقق من اسم المستخدم وكلمة المرور</li>
                  <li>جرب Windows Authentication أولاً</li>
                </ul>
              </div>
              <div>
                <strong>🔧 خطوات تفعيل SQL Server للاتصالات (مهم جداً):</strong>
                <ol className="mt-1 list-decimal list-inside space-y-1 text-xs">
                  <li><strong>افتح SQL Server Configuration Manager</strong>
                    <br />• ابحث عن "SQL Server Configuration Manager" في Start Menu
                  </li>
                  <li><strong>انتقل إلى SQL Server Network Configuration</strong>
                    <br />• اختر "Protocols for SQLEXPRESS"
                  </li>
                  <li><strong>فعّل TCP/IP Protocol</strong>
                    <br />• انقر بالزر الأيمن على TCP/IP → Enable
                    <br />• انقر بالزر الأيمن على TCP/IP → Properties
                    <br />• في تبويب IP Addresses، تأكد من أن IPAll Port = 1433
                  </li>
                  <li><strong>أعد تشغيل SQL Server Service</strong>
                    <br />• انتقل إلى SQL Server Services
                    <br />• انقر بالزر الأيمن على SQL Server (SQLEXPRESS) → Restart
                  </li>
                  <li><strong>تأكد من أن SQL Server Browser يعمل</strong>
                    <br />• انقر بالزر الأيمن على SQL Server Browser → Start
                    <br />• اضبطه على Automatic startup
                  </li>
                  <li><strong>تحقق من Windows Firewall</strong>
                    <br />• أضف استثناء للمنفذ 1433
                    <br />• أو أضف استثناء لـ sqlservr.exe
                  </li>
                </ol>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            فهمت، شكراً
          </button>
        </div>
      </div>
    </div>
  );
}
