import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const providers = {
      gemini: {
        available: !!(process.env.GOOGLE_GEMINI_API_KEY || process.env.GEMINI_API_KEY),
        error: !(process.env.GOOGLE_GEMINI_API_KEY || process.env.GEMINI_API_KEY) 
          ? 'GOOGLE_GEMINI_API_KEY غير موجود في متغيرات البيئة' 
          : undefined
      },
      openrouter: {
        available: !!process.env.OPENROUTER_API_KEY,
        error: !process.env.OPENROUTER_API_KEY 
          ? 'OPENROUTER_API_KEY غير موجود في متغيرات البيئة' 
          : undefined
      }
    };

    return NextResponse.json({
      success: true,
      providers
    });
  } catch (error) {
    console.error('خطأ في فحص توفر النماذج:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'خطأ في فحص توفر النماذج' 
      },
      { status: 500 }
    );
  }
}
