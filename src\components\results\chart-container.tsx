"use client"

import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>hart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { <PERSON><PERSON><PERSON>3, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, Download } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChartContainerProps {
  data: any[];
  title?: string;
  xKey: string;
  yKey: string;
  chartType?: 'bar' | 'line' | 'pie';
  className?: string;
  height?: number;
  colors?: string[];
}

export function ChartContainer({
  data,
  title,
  xKey,
  yKey,
  chartType = 'bar',
  className = "",
  height = 400,
  colors = [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4',
    '#EC4899', '#84CC16', '#F97316', '#6366F1', '#14B8A6', '#F43F5E'
  ]
}: ChartContainerProps) {
  const [selectedChartType, setSelectedChartType] = useState<'bar' | 'line' | 'pie'>(chartType);

  const formatValue = (value: any) => {
    if (typeof value === 'number') {
      return value.toLocaleString('ar-SA');
    }
    return value;
  };

  const exportChart = () => {
    // This is a placeholder for chart export functionality
    // In a real implementation, you might use libraries like html2canvas
    console.log('تصدير الرسم البياني');
  };

  const renderChart = () => {
    switch (selectedChartType) {
      case 'bar':
        return (
          <BarChart data={data}>
            <defs>
              <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor={colors[0]} stopOpacity={0.8}/>
                <stop offset="100%" stopColor={colors[0]} stopOpacity={0.3}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey={xKey}
              tick={{ fontSize: 12, fill: '#6B7280' }}
              angle={-45}
              textAnchor="end"
              height={60}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis
              tick={{ fontSize: 12, fill: '#6B7280' }}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <Tooltip
              formatter={(value) => [formatValue(value), yKey]}
              labelFormatter={(label) => `${xKey}: ${label}`}
              contentStyle={{
                backgroundColor: '#ffffff',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Legend />
            <Bar
              dataKey={yKey}
              fill="url(#barGradient)"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        );

      case 'line':
        return (
          <LineChart data={data}>
            <defs>
              <linearGradient id="lineGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor={colors[0]} stopOpacity={0.3}/>
                <stop offset="100%" stopColor={colors[0]} stopOpacity={0.05}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey={xKey}
              tick={{ fontSize: 12, fill: '#6B7280' }}
              angle={-45}
              textAnchor="end"
              height={60}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis
              tick={{ fontSize: 12, fill: '#6B7280' }}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <Tooltip
              formatter={(value) => [formatValue(value), yKey]}
              labelFormatter={(label) => `${xKey}: ${label}`}
              contentStyle={{
                backgroundColor: '#ffffff',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey={yKey}
              stroke={colors[0]}
              strokeWidth={3}
              dot={{ fill: colors[0], strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: colors[0], strokeWidth: 2, fill: '#ffffff' }}
            />
          </LineChart>
        );

      case 'pie':
        return (
          <PieChart>
            <defs>
              {colors.map((color, index) => (
                <linearGradient key={index} id={`pieGradient${index}`} x1="0" y1="0" x2="1" y2="1">
                  <stop offset="0%" stopColor={color} stopOpacity={0.9}/>
                  <stop offset="100%" stopColor={color} stopOpacity={0.6}/>
                </linearGradient>
              ))}
            </defs>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={130}
              innerRadius={40}
              paddingAngle={2}
              fill="#8884d8"
              dataKey={yKey}
              nameKey={xKey}
              animationBegin={0}
              animationDuration={800}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={`url(#pieGradient${index % colors.length})`}
                  stroke="#ffffff"
                  strokeWidth={2}
                />
              ))}
            </Pie>
            <Tooltip
              formatter={(value) => formatValue(value)}
              contentStyle={{
                backgroundColor: '#ffffff',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Legend
              verticalAlign="bottom"
              height={36}
              iconType="circle"
              wrapperStyle={{
                paddingTop: '20px',
                fontSize: '14px'
              }}
            />
          </PieChart>
        );

      default:
        return null;
    }
  };

  if (!data || data.length === 0) {
    return (
      <div className={cn("bg-white rounded-lg shadow-md p-8 text-center", className)}>
        <div className="text-gray-500">
          <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">لا توجد بيانات للرسم البياني</h3>
          <p>لم يتم العثور على بيانات مناسبة لإنشاء رسم بياني</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("bg-white rounded-lg shadow-lg overflow-hidden border border-gray-100", className)}>
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <div>
              {title && (
                <h3 className="text-xl font-bold text-gray-900 mb-1">{title}</h3>
              )}
              <p className="text-sm text-gray-600">
                {data.length} عنصر • رسم بياني تفاعلي
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Chart Type Selector */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setSelectedChartType('bar')}
                className={cn(
                  "p-2 rounded-md transition-colors",
                  selectedChartType === 'bar' 
                    ? "bg-white text-blue-600 shadow-sm" 
                    : "text-gray-600 hover:text-gray-800"
                )}
                title="رسم بياني عمودي"
              >
                <BarChart3 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setSelectedChartType('line')}
                className={cn(
                  "p-2 rounded-md transition-colors",
                  selectedChartType === 'line' 
                    ? "bg-white text-blue-600 shadow-sm" 
                    : "text-gray-600 hover:text-gray-800"
                )}
                title="رسم بياني خطي"
              >
                <LineChartIcon className="w-4 h-4" />
              </button>
              <button
                onClick={() => setSelectedChartType('pie')}
                className={cn(
                  "p-2 rounded-md transition-colors",
                  selectedChartType === 'pie' 
                    ? "bg-white text-blue-600 shadow-sm" 
                    : "text-gray-600 hover:text-gray-800"
                )}
                title="رسم بياني دائري"
              >
                <PieChartIcon className="w-4 h-4" />
              </button>
            </div>

            <button
              onClick={exportChart}
              className="flex items-center gap-1 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
            >
              <Download className="w-4 h-4" />
              تصدير
            </button>
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="p-4">
        <ResponsiveContainer width="100%" height={height}>
          {renderChart()}
        </ResponsiveContainer>
      </div>

      {/* Chart Info */}
      <div className="px-4 pb-4">
        <div className="text-xs text-gray-500 flex items-center justify-between">
          <span>المحور السيني: {xKey}</span>
          <span>المحور الصادي: {yKey}</span>
        </div>
      </div>
    </div>
  );
}
