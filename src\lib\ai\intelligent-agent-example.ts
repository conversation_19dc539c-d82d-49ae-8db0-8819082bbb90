// مثال على استخدام الوكيل الذكي

import { GoogleGeminiClient } from './google-gemini-client';

/**
 * مثال على كيفية استخدام الوكيل الذكي الجديد
 */
export async function demonstrateIntelligentAgent() {
  console.log('🚀 بدء عرض الوكيل الذكي...');

  // الحصول على عميل Gemini
  const geminiClient = GoogleGeminiClient.getInstance();

  // مثال على جداول قاعدة البيانات
  const sampleTables = [
    {
      name: 'customers',
      description: 'جدول العملاء يحتوي على معلومات العملاء الأساسية',
      columns: [
        { name: 'customer_id', type: 'int' },
        { name: 'customer_name', type: 'varchar' },
        { name: 'email', type: 'varchar' },
        { name: 'phone', type: 'varchar' }
      ],
      relationships: []
    },
    {
      name: 'items',
      description: 'جدول المنتجات يحتوي على معلومات المنتجات والأسعار',
      columns: [
        { name: 'item_id', type: 'int' },
        { name: 'item_name', type: 'varchar' },
        { name: 'unit_price', type: 'decimal' },
        { name: 'category', type: 'varchar' }
      ],
      relationships: []
    },
    {
      name: 'invoices',
      description: 'جدول الفواتير يحتوي على رؤوس الفواتير للعملاء',
      columns: [
        { name: 'invoice_id', type: 'int' },
        { name: 'customer_id', type: 'int' },
        { name: 'invoice_date', type: 'datetime' },
        { name: 'total_amount', type: 'decimal' }
      ],
      relationships: [
        { fromColumn: 'customer_id', toTable: 'customers', toColumn: 'customer_id' }
      ]
    },
    {
      name: 'invoice_details',
      description: 'جدول تفاصيل الفواتير يحتوي على المنتجات المباعة في كل فاتورة',
      columns: [
        { name: 'invoice_detail_id', type: 'int' },
        { name: 'invoice_id', type: 'int' },
        { name: 'item_id', type: 'int' },
        { name: 'quantity', type: 'int' },
        { name: 'unit_price', type: 'decimal' }
      ],
      relationships: [
        { fromColumn: 'invoice_id', toTable: 'invoices', toColumn: 'invoice_id' },
        { fromColumn: 'item_id', toTable: 'items', toColumn: 'item_id' }
      ]
    }
  ];

  try {
    // إنشاء الوكيل الذكي
    await geminiClient.initializeIntelligentAgent(sampleTables);

    // أمثلة على الأسئلة التي يمكن للوكيل الذكي فهمها
    const testQuestions = [
      "ما هي مبيعات التفاح في فبراير؟",
      "أكثر العملاء شراءً هذا الشهر",
      "إجمالي مبيعات البرتقال",
      "مشتريات العميل أحمد محمد",
      "أكثر المنتجات مبيعاً في الربع الأول"
    ];

    console.log('\n🧠 اختبار الوكيل الذكي مع أسئلة مختلفة:\n');

    for (const question of testQuestions) {
      console.log(`\n📝 السؤال: "${question}"`);
      console.log('⏳ جاري التحليل...');

      try {
        const result = await geminiClient.generateSQLQuery(
          question,
          sampleTables,
          'mysql'
        );

        console.log('✅ النتيجة:');
        console.log(`📊 الاستعلام: ${result.query}`);
        console.log(`💡 الشرح: ${result.explanation}`);
        console.log(`🎯 مستوى الثقة: ${(result.confidence * 100).toFixed(1)}%`);
        console.log('─'.repeat(80));

      } catch (error) {
        console.error(`❌ خطأ في معالجة السؤال: ${error}`);
      }
    }

  } catch (error) {
    console.error('❌ فشل في إنشاء الوكيل الذكي:', error);
  }
}

/**
 * مقارنة بين النظام القديم والوكيل الذكي الجديد
 */
export async function compareOldVsNewSystem() {
  console.log('\n🔄 مقارنة بين النظام القديم والوكيل الذكي الجديد:\n');

  const testQuestion = "مبيعات البرتقال";

  console.log(`📝 السؤال المختبر: "${testQuestion}"`);
  console.log('\n' + '='.repeat(80));

  // النظام القديم (مع الكلمات المفتاحية)
  console.log('\n❌ النظام القديم:');
  console.log('🔍 يبحث عن كلمة "مبيعات" في قائمة ثابتة');
  console.log('📋 يجد مثال "مبيعات فترة زمنية"');
  console.log('🤖 يستخدم استعلام جاهز بدون فهم');
  console.log('❌ النتيجة: استعلام خاطئ لا يتعلق بالبرتقال');

  console.log('\n✅ الوكيل الذكي الجديد:');
  console.log('🧠 يحلل السؤال لغوياً: "مبيعات" + "البرتقال"');
  console.log('🎯 يفهم أن "البرتقال" منتج');
  console.log('📊 يحدد الجداول المطلوبة: items, invoice_details, invoices');
  console.log('⚡ ينشئ استعلام مخصص: WHERE item_name LIKE \'%برتقال%\'');
  console.log('✅ النتيجة: استعلام صحيح ومخصص للسؤال');

  console.log('\n' + '='.repeat(80));
  console.log('\n🎉 الخلاصة:');
  console.log('✅ الوكيل الذكي يفهم السياق');
  console.log('✅ يعمل مع أي قاعدة بيانات');
  console.log('✅ لا يحتاج كلمات مفتاحية');
  console.log('✅ ينشئ استعلامات مخصصة');
  console.log('✅ ذكاء اصطناعي حقيقي!');
}

/**
 * اختبار الوكيل مع قواعد بيانات مختلفة
 */
export async function testWithDifferentDomains() {
  console.log('\n🏥 اختبار الوكيل مع قاعدة بيانات مستشفى:\n');

  const hospitalTables = [
    {
      name: 'patients',
      description: 'جدول المرضى يحتوي على معلومات المرضى الأساسية',
      columns: [
        { name: 'patient_id', type: 'int' },
        { name: 'patient_name', type: 'varchar' },
        { name: 'age', type: 'int' },
        { name: 'diagnosis', type: 'varchar' }
      ],
      relationships: []
    },
    {
      name: 'appointments',
      description: 'جدول المواعيد يحتوي على مواعيد المرضى مع الأطباء',
      columns: [
        { name: 'appointment_id', type: 'int' },
        { name: 'patient_id', type: 'int' },
        { name: 'doctor_name', type: 'varchar' },
        { name: 'appointment_date', type: 'datetime' }
      ],
      relationships: [
        { fromColumn: 'patient_id', toTable: 'patients', toColumn: 'patient_id' }
      ]
    }
  ];

  const geminiClient = GoogleGeminiClient.getInstance();
  await geminiClient.initializeIntelligentAgent(hospitalTables);

  const hospitalQuestions = [
    "مرضى السكري",
    "مواعيد الدكتور أحمد",
    "أكثر الأمراض شيوعاً"
  ];

  for (const question of hospitalQuestions) {
    console.log(`\n📝 السؤال: "${question}"`);
    try {
      const result = await geminiClient.generateSQLQuery(
        question,
        hospitalTables,
        'mysql'
      );
      console.log(`✅ الاستعلام: ${result.query}`);
    } catch (error) {
      console.error(`❌ خطأ: ${error}`);
    }
  }

  console.log('\n🎓 نفس الوكيل يعمل مع قاعدة بيانات مدرسة أو أي مجال آخر!');
}

// تشغيل العرض التوضيحي
if (require.main === module) {
  (async () => {
    await demonstrateIntelligentAgent();
    await compareOldVsNewSystem();
    await testWithDifferentDomains();
  })();
}
