// إعدادات الشبكة والاتصال المحسنة لـ Google Gemini API

export interface NetworkConfig {
  timeout: number;
  retries: number;
  retryDelay: number;
  maxRetryDelay: number;
  backoffMultiplier: number;
}

export const DEFAULT_NETWORK_CONFIG: NetworkConfig = {
  timeout: 60000, // 60 ثانية
  retries: 3,
  retryDelay: 2000, // 2 ثانية
  maxRetryDelay: 30000, // 30 ثانية كحد أقصى
  backoffMultiplier: 2
};

// فحص حالة الشبكة
export async function checkNetworkConnectivity(): Promise<boolean> {
  try {
    // محاولة الاتصال بـ Google DNS للتأكد من وجود اتصال إنترنت
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch('https://dns.google/resolve?name=google.com&type=A', {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Accept': 'application/json'
      }
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.warn('فحص الشبكة فشل:', error);
    return false;
  }
}

// فحص إمكانية الوصول لـ Google AI API
export async function checkGeminiAPIAccess(apiKey: string): Promise<{
  accessible: boolean;
  error?: string;
}> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models', {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      return { accessible: true };
    } else {
      const errorText = await response.text();
      return { 
        accessible: false, 
        error: `HTTP ${response.status}: ${errorText}` 
      };
    }
  } catch (error: any) {
    return { 
      accessible: false, 
      error: error?.message || 'خطأ في الاتصال' 
    };
  }
}

// تشخيص مشاكل الشبكة
export async function diagnoseNetworkIssues(): Promise<{
  internetConnection: boolean;
  dnsResolution: boolean;
  geminiAPIAccess: boolean;
  recommendations: string[];
}> {
  const results = {
    internetConnection: false,
    dnsResolution: false,
    geminiAPIAccess: false,
    recommendations: [] as string[]
  };

  // فحص الاتصال بالإنترنت
  results.internetConnection = await checkNetworkConnectivity();
  if (!results.internetConnection) {
    results.recommendations.push('تأكد من اتصالك بالإنترنت');
    return results;
  }

  // فحص DNS
  try {
    await fetch('https://generativelanguage.googleapis.com', { 
      method: 'HEAD',
      signal: AbortSignal.timeout(5000)
    });
    results.dnsResolution = true;
  } catch (error) {
    results.recommendations.push('مشكلة في DNS - جرب تغيير DNS إلى 8.8.8.8');
  }

  // فحص الوصول لـ Gemini API
  const apiKey = process.env.GEMINI_API_KEY;
  if (apiKey) {
    const apiCheck = await checkGeminiAPIAccess(apiKey);
    results.geminiAPIAccess = apiCheck.accessible;
    
    if (!apiCheck.accessible) {
      results.recommendations.push(`مشكلة في API: ${apiCheck.error}`);
      
      if (apiCheck.error?.includes('403') || apiCheck.error?.includes('401')) {
        results.recommendations.push('تحقق من صحة API Key');
      }
      
      if (apiCheck.error?.includes('429')) {
        results.recommendations.push('تم تجاوز حد الطلبات - انتظر قليلاً');
      }
    }
  } else {
    results.recommendations.push('GEMINI_API_KEY غير موجود');
  }

  return results;
}

// إعدادات Proxy إذا كان مطلوباً
export function getProxyConfig(): {
  httpProxy?: string;
  httpsProxy?: string;
  noProxy?: string;
} {
  return {
    httpProxy: process.env.HTTP_PROXY || process.env.http_proxy,
    httpsProxy: process.env.HTTPS_PROXY || process.env.https_proxy,
    noProxy: process.env.NO_PROXY || process.env.no_proxy
  };
}

// دالة مساعدة لإنشاء fetch مع إعدادات محسنة
export function createEnhancedFetch(config: Partial<NetworkConfig> = {}) {
  const finalConfig = { ...DEFAULT_NETWORK_CONFIG, ...config };

  return async function enhancedFetch(
    url: string, 
    options: RequestInit = {}
  ): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), finalConfig.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'User-Agent': 'SLT-AI-Agent/1.0',
          ...options.headers
        }
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  };
}
