"use client"

import React, { useState } from 'react';
import { Play, CheckCircle, XCircle, AlertTriangle, Loader2, Info } from 'lucide-react';
import { DatabaseConnection } from '@/lib/database/types';

interface ConnectionDiagnosticsProps {
  config: DatabaseConnection;
  onClose: () => void;
}

interface DiagnosticStep {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error' | 'warning';
  message?: string;
  details?: string;
}

export function ConnectionDiagnostics({ config, onClose }: ConnectionDiagnosticsProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [steps, setSteps] = useState<DiagnosticStep[]>([
    { name: 'فحص إعدادات الاتصال', status: 'pending' },
    { name: 'اختبار الوصول للخادم', status: 'pending' },
    { name: 'فحص TCP/IP Protocol', status: 'pending' },
    { name: 'اختبار المصادقة', status: 'pending' },
    { name: 'اختبار الاتصال بقاعدة البيانات', status: 'pending' }
  ]);

  const updateStep = (index: number, updates: Partial<DiagnosticStep>) => {
    setSteps(prev => prev.map((step, i) => 
      i === index ? { ...step, ...updates } : step
    ));
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    
    // الخطوة 1: فحص الإعدادات
    updateStep(0, { status: 'running' });
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (!config.host) {
      updateStep(0, { 
        status: 'error', 
        message: 'اسم الخادم مطلوب',
        details: 'يجب إدخال اسم الخادم مثل AHMED\\SQLEXPRESS'
      });
      setIsRunning(false);
      return;
    }
    
    updateStep(0, { 
      status: 'success', 
      message: `الخادم: ${config.host}`,
      details: `نوع المصادقة: ${config.useWindowsAuth ? 'Windows Authentication' : 'SQL Server Authentication'}`
    });

    // الخطوة 2: اختبار الوصول للخادم
    updateStep(1, { status: 'running' });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    try {
      const response = await fetch('/api/database/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...config,
          database: 'master' // استخدام master للاختبار
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        updateStep(1, { status: 'success', message: 'تم الوصول للخادم بنجاح' });
        updateStep(2, { status: 'success', message: 'TCP/IP يعمل بشكل صحيح' });
        updateStep(3, { status: 'success', message: 'المصادقة تمت بنجاح' });
        updateStep(4, { status: 'success', message: 'الاتصال بقاعدة البيانات ناجح' });
      } else {
        const errorMsg = result.error || 'خطأ غير معروف';
        
        if (errorMsg.includes('ETIMEOUT') || errorMsg.includes('15000ms')) {
          updateStep(1, { 
            status: 'error', 
            message: 'انتهت مهلة الاتصال',
            details: 'SQL Server لا يستجيب. تحقق من أن TCP/IP مفعل وأن SQL Server Browser يعمل'
          });
          updateStep(2, { 
            status: 'error', 
            message: 'TCP/IP غير مفعل أو SQL Server Browser لا يعمل',
            details: 'افتح SQL Server Configuration Manager وفعّل TCP/IP Protocol'
          });
        } else if (errorMsg.includes('ESOCKET')) {
          updateStep(1, { 
            status: 'error', 
            message: 'لا يمكن الوصول للخادم',
            details: 'تحقق من اسم الخادم وأن SQL Server يعمل'
          });
        } else if (errorMsg.includes('ELOGIN')) {
          updateStep(1, { status: 'success', message: 'تم الوصول للخادم' });
          updateStep(2, { status: 'success', message: 'TCP/IP يعمل' });
          updateStep(3, { 
            status: 'error', 
            message: 'خطأ في المصادقة',
            details: 'تحقق من اسم المستخدم وكلمة المرور أو جرب Windows Authentication'
          });
        } else {
          updateStep(1, { 
            status: 'error', 
            message: 'خطأ في الاتصال',
            details: errorMsg
          });
        }
      }
    } catch (error) {
      updateStep(1, { 
        status: 'error', 
        message: 'خطأ في الشبكة',
        details: 'تحقق من اتصال الإنترنت والإعدادات'
      });
    }
    
    setIsRunning(false);
  };

  const getStatusIcon = (status: DiagnosticStep['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-600" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      default:
        return <div className="w-4 h-4 rounded-full border-2 border-gray-300" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <Info className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">
              تشخيص الاتصال بـ SQL Server
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            ×
          </button>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <h3 className="font-medium text-gray-900 mb-2">إعدادات الاتصال:</h3>
            <div className="bg-gray-50 p-3 rounded text-sm">
              <div>الخادم: <strong>{config.host}</strong></div>
              <div>المصادقة: <strong>{config.useWindowsAuth ? 'Windows Authentication' : 'SQL Server Authentication'}</strong></div>
              <div>قاعدة البيانات: <strong>{config.database}</strong></div>
            </div>
          </div>

          <div className="space-y-4">
            {steps.map((step, index) => (
              <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                {getStatusIcon(step.status)}
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{step.name}</div>
                  {step.message && (
                    <div className={`text-sm mt-1 ${
                      step.status === 'success' ? 'text-green-600' :
                      step.status === 'error' ? 'text-red-600' :
                      step.status === 'warning' ? 'text-yellow-600' :
                      'text-gray-600'
                    }`}>
                      {step.message}
                    </div>
                  )}
                  {step.details && (
                    <div className="text-xs text-gray-500 mt-1 bg-gray-50 p-2 rounded">
                      {step.details}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 flex gap-3">
            <button
              onClick={runDiagnostics}
              disabled={isRunning}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isRunning ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              {isRunning ? 'جاري التشخيص...' : 'بدء التشخيص'}
            </button>
            
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              إغلاق
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
