import { NextRequest, NextResponse } from 'next/server';
import { MSSQLSchemaExtractor } from '@/lib/database/mssql-extractor';
import { MySQLSchemaExtractor } from '@/lib/database/mysql-extractor';
import { DatabaseConnection } from '@/lib/database/types';

export async function POST(request: NextRequest) {
  try {
    const connection: Omit<DatabaseConnection, 'database'> = await request.json();

    // التحقق من صحة البيانات
    if (!connection.host) {
      return NextResponse.json(
        { success: false, error: 'عنوان الخادم مطلوب' },
        { status: 400 }
      );
    }

    // للـ SQL Server، التحقق من المصادقة
    if (connection.type === 'mssql' && !connection.useWindowsAuth) {
      if (!connection.username) {
        return NextResponse.json(
          { success: false, error: 'اسم المستخدم مطلوب لـ SQL Server Authentication' },
          { status: 400 }
        );
      }
    }

    let databases: string[] = [];

    if (connection.type === 'mssql') {
      const extractor = new MSSQLSchemaExtractor();
      
      try {
        // الاتصال بدون تحديد قاعدة بيانات
        await extractor.connectWithoutDatabase(connection);
        
        // الحصول على قائمة قواعد البيانات
        databases = await extractor.getDatabases();
        
        // قطع الاتصال
        await extractor.disconnect();
        
      } catch (error) {
        await extractor.disconnect();
        throw error;
      }
      
    } else if (connection.type === 'mysql') {
      // للـ MySQL، نحتاج للاتصال بقاعدة بيانات افتراضية أولاً
      const extractor = new MySQLSchemaExtractor();

      try {
        // الاتصال بقاعدة بيانات information_schema
        const tempConnection = {
          ...connection,
          database: 'information_schema'
        } as DatabaseConnection;

        await extractor.connect(tempConnection);

        // الحصول على قائمة قواعد البيانات
        databases = await extractor.getDatabases();

        // قطع الاتصال
        await extractor.disconnect();

      } catch (error) {
        await extractor.disconnect();
        throw error;
      }
      
    } else {
      return NextResponse.json(
        { success: false, error: 'نوع قاعدة البيانات غير مدعوم' },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      databases,
      count: databases.length
    });

  } catch (error) {
    console.error('خطأ في الحصول على قواعد البيانات:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'خطأ غير معروف' 
      },
      { status: 500 }
    );
  }
}
