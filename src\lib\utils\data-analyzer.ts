// نظام تحليل البيانات وتوليد الخلاصات الذكية

import { translateColumn } from './column-translator';

export interface DataInsight {
  type: 'trend' | 'ranking' | 'comparison' | 'summary' | 'anomaly';
  title: string;
  description: string;
  value?: string | number;
  icon: string;
  color: string;
}

export interface SmartSummary {
  title: string;
  description: string;
  insights: DataInsight[];
  keyMetrics: {
    label: string;
    value: string;
    change?: string;
    trend?: 'up' | 'down' | 'stable';
  }[];
  recommendations: string[];
}

// دالة لتحليل البيانات وتوليد خلاصة ذكية
export function generateSmartSummary(data: any[], query?: string): SmartSummary {
  if (!data || data.length === 0) {
    return {
      title: 'لا توجد بيانات للتحليل',
      description: 'لم يتم العثور على أي بيانات لتحليلها.',
      insights: [],
      keyMetrics: [],
      recommendations: []
    };
  }

  const columns = Object.keys(data[0]);
  const numericColumns = columns.filter(col => 
    data.some(row => typeof row[col] === 'number' && !isNaN(row[col]))
  );

  // تحديد نوع التحليل بناءً على البيانات والاستعلام
  const analysisType = detectAnalysisType(data, query);
  
  switch (analysisType) {
    case 'customer_ranking':
      return generateCustomerRankingSummary(data, numericColumns);
    case 'product_analysis':
      return generateProductAnalysisSummary(data, numericColumns);
    case 'sales_analysis':
      return generateSalesAnalysisSummary(data, numericColumns);
    case 'financial_summary':
      return generateFinancialSummary(data, numericColumns);
    default:
      return generateGenericSummary(data, numericColumns);
  }
}

// دالة لتحديد نوع التحليل
function detectAnalysisType(data: any[], query?: string): string {
  const columns = Object.keys(data[0]).map(col => col.toLowerCase());
  const queryLower = query?.toLowerCase() || '';

  if (queryLower.includes('عميل') || queryLower.includes('customer') || 
      columns.some(col => col.includes('customer'))) {
    return 'customer_ranking';
  }

  if (queryLower.includes('منتج') || queryLower.includes('item') || 
      columns.some(col => col.includes('item'))) {
    return 'product_analysis';
  }

  if (queryLower.includes('مبيعات') || queryLower.includes('فاتورة') || 
      columns.some(col => col.includes('invoice') || col.includes('sales'))) {
    return 'sales_analysis';
  }

  if (columns.some(col => col.includes('amount') || col.includes('total') || col.includes('revenue'))) {
    return 'financial_summary';
  }

  return 'generic';
}

// تحليل ترتيب العملاء
function generateCustomerRankingSummary(data: any[], numericColumns: string[]): SmartSummary {
  const totalCustomers = data.length;
  const valueColumn = numericColumns.find(col => 
    col.includes('total') || col.includes('amount') || col.includes('purchases')
  ) || numericColumns[0];

  const totalValue = data.reduce((sum, row) => sum + (row[valueColumn] || 0), 0);
  const avgValue = totalValue / totalCustomers;
  const topCustomer = data[0];
  const topValue = topCustomer?.[valueColumn] || 0;

  // حساب التوزيع
  const top20Percent = Math.ceil(totalCustomers * 0.2);
  const top20Value = data.slice(0, top20Percent).reduce((sum, row) => sum + (row[valueColumn] || 0), 0);
  const top20Percentage = ((top20Value / totalValue) * 100).toFixed(1);

  return {
    title: 'تحليل أداء العملاء',
    description: `تم تحليل بيانات ${totalCustomers} عميل لتحديد أنماط الشراء والعملاء الأكثر قيمة للشركة.`,
    insights: [
      {
        type: 'ranking',
        title: 'العميل الأول',
        description: `${topCustomer?.customer_name || topCustomer?.اسم_العميل || 'العميل الأول'} يتصدر القائمة`,
        value: `${topValue.toLocaleString('ar-SA')} ر.س`,
        icon: '👑',
        color: 'text-yellow-600'
      },
      {
        type: 'trend',
        title: 'قاعدة العملاء المميزين',
        description: `أفضل 20% من العملاء يساهمون بـ ${top20Percentage}% من إجمالي المبيعات`,
        icon: '📊',
        color: 'text-blue-600'
      },
      {
        type: 'comparison',
        title: 'متوسط القيمة',
        description: 'متوسط مشتريات العميل الواحد',
        value: `${avgValue.toLocaleString('ar-SA')} ر.س`,
        icon: '📈',
        color: 'text-green-600'
      }
    ],
    keyMetrics: [
      {
        label: 'إجمالي العملاء',
        value: totalCustomers.toLocaleString('ar-SA')
      },
      {
        label: 'إجمالي المبيعات',
        value: `${totalValue.toLocaleString('ar-SA')} ر.س`
      },
      {
        label: 'متوسط المشتريات',
        value: `${avgValue.toLocaleString('ar-SA')} ر.س`
      }
    ],
    recommendations: [
      'ركز على الاحتفاظ بالعملاء الأوائل من خلال برامج الولاء',
      'ادرس سلوك العملاء المميزين لتطبيق استراتيجيات مماثلة مع عملاء آخرين',
      'قم بتطوير عروض خاصة للعملاء ذوي القيمة المنخفضة لزيادة مشترياتهم'
    ]
  };
}

// تحليل المنتجات
function generateProductAnalysisSummary(data: any[], numericColumns: string[]): SmartSummary {
  const totalProducts = data.length;
  const quantityColumn = numericColumns.find(col => 
    col.includes('sold') || col.includes('quantity')
  ) || numericColumns[0];
  const revenueColumn = numericColumns.find(col => 
    col.includes('revenue') || col.includes('total')
  ) || numericColumns[1];

  const totalQuantity = data.reduce((sum, row) => sum + (row[quantityColumn] || 0), 0);
  const totalRevenue = data.reduce((sum, row) => sum + (row[revenueColumn] || 0), 0);
  const topProduct = data[0];

  return {
    title: 'تحليل أداء المنتجات',
    description: `تحليل شامل لأداء ${totalProducts} منتج من حيث المبيعات والإيرادات.`,
    insights: [
      {
        type: 'ranking',
        title: 'المنتج الأكثر مبيعاً',
        description: `${topProduct?.item_name || topProduct?.اسم_المنتج || 'المنتج الأول'} يتصدر المبيعات`,
        value: `${(topProduct?.[quantityColumn] || 0).toLocaleString('ar-SA')} وحدة`,
        icon: '🏆',
        color: 'text-yellow-600'
      },
      {
        type: 'summary',
        title: 'إجمالي الوحدات المباعة',
        description: 'مجموع جميع الوحدات المباعة',
        value: `${totalQuantity.toLocaleString('ar-SA')} وحدة`,
        icon: '📦',
        color: 'text-blue-600'
      },
      {
        type: 'summary',
        title: 'إجمالي الإيرادات',
        description: 'مجموع إيرادات جميع المنتجات',
        value: `${totalRevenue.toLocaleString('ar-SA')} ر.س`,
        icon: '💰',
        color: 'text-green-600'
      }
    ],
    keyMetrics: [
      {
        label: 'عدد المنتجات',
        value: totalProducts.toLocaleString('ar-SA')
      },
      {
        label: 'إجمالي الوحدات',
        value: totalQuantity.toLocaleString('ar-SA')
      },
      {
        label: 'إجمالي الإيرادات',
        value: `${totalRevenue.toLocaleString('ar-SA')} ر.س`
      }
    ],
    recommendations: [
      'ركز على تسويق المنتجات الأكثر مبيعاً لزيادة الإيرادات',
      'ادرس أسباب نجاح المنتجات الرائدة وطبقها على منتجات أخرى',
      'راجع استراتيجية التسعير للمنتجات ذات المبيعات المنخفضة'
    ]
  };
}

// تحليل المبيعات العام
function generateSalesAnalysisSummary(data: any[], numericColumns: string[]): SmartSummary {
  const totalRecords = data.length;
  const valueColumn = numericColumns[0];
  const totalValue = data.reduce((sum, row) => sum + (row[valueColumn] || 0), 0);
  const avgValue = totalValue / totalRecords;

  return {
    title: 'تحليل المبيعات',
    description: `تحليل شامل لبيانات المبيعات يشمل ${totalRecords} سجل.`,
    insights: [
      {
        type: 'summary',
        title: 'إجمالي المبيعات',
        description: 'مجموع جميع المبيعات في الفترة المحددة',
        value: `${totalValue.toLocaleString('ar-SA')} ر.س`,
        icon: '💼',
        color: 'text-green-600'
      },
      {
        type: 'comparison',
        title: 'متوسط قيمة المعاملة',
        description: 'متوسط قيمة كل معاملة',
        value: `${avgValue.toLocaleString('ar-SA')} ر.س`,
        icon: '📊',
        color: 'text-blue-600'
      }
    ],
    keyMetrics: [
      {
        label: 'عدد المعاملات',
        value: totalRecords.toLocaleString('ar-SA')
      },
      {
        label: 'إجمالي القيمة',
        value: `${totalValue.toLocaleString('ar-SA')} ر.س`
      },
      {
        label: 'متوسط المعاملة',
        value: `${avgValue.toLocaleString('ar-SA')} ر.س`
      }
    ],
    recommendations: [
      'راقب الاتجاهات الشهرية للمبيعات لتحديد المواسم الذهبية',
      'حلل أسباب المعاملات عالية القيمة لتكرارها',
      'ضع استراتيجيات لزيادة متوسط قيمة المعاملة'
    ]
  };
}

// تحليل مالي عام
function generateFinancialSummary(data: any[], numericColumns: string[]): SmartSummary {
  const totalRecords = data.length;
  const mainColumn = numericColumns[0];
  const total = data.reduce((sum, row) => sum + (row[mainColumn] || 0), 0);

  return {
    title: 'الملخص المالي',
    description: `تحليل مالي شامل للبيانات المتاحة.`,
    insights: [
      {
        type: 'summary',
        title: 'الإجمالي العام',
        description: 'مجموع جميع القيم',
        value: `${total.toLocaleString('ar-SA')} ر.س`,
        icon: '💰',
        color: 'text-green-600'
      }
    ],
    keyMetrics: [
      {
        label: 'عدد السجلات',
        value: totalRecords.toLocaleString('ar-SA')
      },
      {
        label: 'الإجمالي',
        value: `${total.toLocaleString('ar-SA')} ر.س`
      }
    ],
    recommendations: [
      'راجع البيانات المالية بانتظام لضمان دقة التقارير',
      'قم بتحليل الاتجاهات المالية لاتخاذ قرارات استراتيجية'
    ]
  };
}

// تحليل عام للبيانات
function generateGenericSummary(data: any[], numericColumns: string[]): SmartSummary {
  const totalRecords = data.length;
  const totalColumns = Object.keys(data[0]).length;

  return {
    title: 'ملخص البيانات',
    description: `تم تحليل ${totalRecords} سجل يحتوي على ${totalColumns} حقل.`,
    insights: [
      {
        type: 'summary',
        title: 'حجم البيانات',
        description: 'إجمالي السجلات المتاحة',
        value: totalRecords.toString(),
        icon: '📋',
        color: 'text-blue-600'
      }
    ],
    keyMetrics: [
      {
        label: 'عدد السجلات',
        value: totalRecords.toLocaleString('ar-SA')
      },
      {
        label: 'عدد الحقول',
        value: totalColumns.toLocaleString('ar-SA')
      }
    ],
    recommendations: [
      'استخدم المرشحات لتحليل البيانات بشكل أكثر تفصيلاً',
      'قم بتصدير البيانات للتحليل المتقدم'
    ]
  };
}
