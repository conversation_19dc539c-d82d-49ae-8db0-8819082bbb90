import { NextResponse } from 'next/server';
import { SchemaManager } from '@/lib/database/schema-manager';

export async function GET() {
  try {
    const schemaManager = SchemaManager.getInstance();
    
    // فحص وجود Schema محسن
    const enrichedSchema = await schemaManager.loadEnrichedSchema();
    
    if (!enrichedSchema) {
      return NextResponse.json({
        exists: false,
        upToDate: false,
        message: 'لا يوجد Schema محسن'
      });
    }

    // فحص ما إذا كان Schema أقدم من 7 أيام (أكثر مرونة)
    const extractedAt = new Date(enrichedSchema.extractedAt);
    const now = new Date();
    const hoursDiff = (now.getTime() - extractedAt.getTime()) / (1000 * 60 * 60);
    const upToDate = hoursDiff < (7 * 24); // 7 أيام

    // إعداد معلومات الجداول
    const tablesInfo = enrichedSchema.tables.map(table => {
      // البحث عن وصف الجدول في tableDescriptions
      const tableDesc = enrichedSchema.tableDescriptions.find(desc => desc.tableName === table.name);

      return {
        name: table.name,
        description: tableDesc?.description || `جدول ${table.name}`,
        domain: tableDesc?.domain || 'عام',
        columnsCount: table.columns?.length || 0,
        rowCount: table.rowCount
      };
    });

    return NextResponse.json({
      exists: true,
      upToDate,
      extractedAt: enrichedSchema.extractedAt,
      tablesCount: enrichedSchema.tables.length,
      tablesInfo,
      hoursSinceExtraction: Math.round(hoursDiff),
      message: upToDate ?
        'Schema محدث ومتاح للاستخدام' :
        'Schema موجود لكنه قديم (أكثر من 7 أيام)'
    });

  } catch (error) {
    console.error('خطأ في فحص Schema:', error);
    return NextResponse.json({
      exists: false,
      upToDate: false,
      error: 'خطأ في فحص Schema'
    }, { status: 500 });
  }
}
