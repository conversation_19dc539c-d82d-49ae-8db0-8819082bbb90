"use client"

import React from 'react';
import { Use<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Check, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  sqlQuery?: string;
  explanation?: string;
  confidence?: number;
  relevantTables?: string[];
  executionTime?: number;
  error?: string;
  results?: any[];
}

interface ChatMessageProps {
  message: ChatMessage;
  onCopyQuery?: (query: string) => void;
}

export function ChatMessageComponent({ message, onCopyQuery }: ChatMessageProps) {
  const [copied, setCopied] = React.useState(false);

  const handleCopyQuery = async () => {
    if (message.sqlQuery && onCopyQuery) {
      onCopyQuery(message.sqlQuery);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return 'text-gray-500';
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceText = (confidence?: number) => {
    if (!confidence) return 'غير محدد';
    if (confidence >= 0.8) return 'عالية';
    if (confidence >= 0.6) return 'متوسطة';
    return 'منخفضة';
  };

  return (
    <div className={cn(
      "flex gap-3 p-4 rounded-lg",
      message.type === 'user' 
        ? "bg-blue-50 border border-blue-200 ml-8" 
        : message.type === 'system'
        ? "bg-yellow-50 border border-yellow-200"
        : "bg-gray-50 border border-gray-200 mr-8"
    )}>
      {/* Avatar */}
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        message.type === 'user' 
          ? "bg-blue-600 text-white" 
          : message.type === 'system'
          ? "bg-yellow-600 text-white"
          : "bg-gray-600 text-white"
      )}>
        {message.type === 'user' ? (
          <User className="w-4 h-4" />
        ) : message.type === 'system' ? (
          <AlertCircle className="w-4 h-4" />
        ) : (
          <Bot className="w-4 h-4" />
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-900">
            {message.type === 'user' ? 'أنت' : message.type === 'system' ? 'النظام' : 'الوكيل الذكي'}
          </span>
          <span className="text-xs text-gray-500">
            {formatTime(message.timestamp)}
          </span>
        </div>

        {/* Message Content */}
        <div className="text-gray-800 whitespace-pre-wrap mb-3">
          {message.content}
        </div>

        {/* Error */}
        {message.error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-3">
            <div className="flex items-center">
              <AlertCircle className="w-4 h-4 text-red-500 ml-2" />
              <span className="text-sm font-medium text-red-800">خطأ:</span>
            </div>
            <p className="text-sm text-red-700 mt-1">{message.error}</p>
          </div>
        )}

        {/* SQL Query */}
        {message.sqlQuery && (
          <div className="bg-gray-900 rounded-md p-4 mb-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-300">استعلام SQL:</span>
              <button
                onClick={handleCopyQuery}
                className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors"
              >
                {copied ? (
                  <>
                    <Check className="w-3 h-3" />
                    تم النسخ
                  </>
                ) : (
                  <>
                    <Copy className="w-3 h-3" />
                    نسخ
                  </>
                )}
              </button>
            </div>
            <pre className="text-sm text-gray-100 overflow-x-auto">
              <code>{message.sqlQuery}</code>
            </pre>
          </div>
        )}

        {/* Explanation */}
        {message.explanation && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-3">
            <h4 className="text-sm font-medium text-blue-800 mb-1">شرح الاستعلام:</h4>
            <p className="text-sm text-blue-700">{message.explanation}</p>
          </div>
        )}

        {/* Metadata */}
        {(message.confidence !== undefined || message.relevantTables || message.executionTime) && (
          <div className="flex flex-wrap gap-4 text-xs text-gray-500">
            {message.confidence !== undefined && (
              <div className="flex items-center gap-1">
                <span>مستوى الثقة:</span>
                <span className={cn("font-medium", getConfidenceColor(message.confidence))}>
                  {getConfidenceText(message.confidence)} ({Math.round(message.confidence * 100)}%)
                </span>
              </div>
            )}
            
            {message.relevantTables && message.relevantTables.length > 0 && (
              <div className="flex items-center gap-1">
                <span>الجداول المستخدمة:</span>
                <span className="font-medium">{message.relevantTables.join(', ')}</span>
              </div>
            )}
            
            {message.executionTime && (
              <div className="flex items-center gap-1">
                <span>وقت المعالجة:</span>
                <span className="font-medium">{message.executionTime}ms</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
