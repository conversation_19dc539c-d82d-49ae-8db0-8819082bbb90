import { NextResponse } from 'next/server';
import { SchemaManager } from '@/lib/database/schema-manager';

export async function GET() {
  try {
    const schemaManager = SchemaManager.getInstance();
    const connection = await schemaManager.loadConnection();
    
    if (!connection) {
      return NextResponse.json({
        connection: null,
        message: 'لا توجد معلومات اتصال محفوظة'
      });
    }

    return NextResponse.json({
      connection,
      message: 'تم تحميل معلومات الاتصال بنجاح'
    });

  } catch (error) {
    console.error('خطأ في تحميل معلومات الاتصال:', error);
    return NextResponse.json({
      connection: null,
      error: 'خطأ في تحميل معلومات الاتصال'
    }, { status: 500 });
  }
}
