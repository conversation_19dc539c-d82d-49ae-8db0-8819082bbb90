"use client"

import React from 'react';
import { Database, Table, Calendar, RefreshCw, Settings } from 'lucide-react';
import { AgentState } from '@/lib/agent/database-agent';
import { cn } from '@/lib/utils';

interface DatabaseInfoProps {
  agentState: AgentState;
  tablesInfo?: Array<{
    name: string;
    description: string;
    domain: string;
    columnsCount: number;
    rowCount?: number;
  }>;
  onRefresh?: () => void;
  onSettings?: () => void;
  className?: string;
}

export function DatabaseInfo({ 
  agentState, 
  tablesInfo = [],
  onRefresh,
  onSettings,
  className = ""
}: DatabaseInfoProps) {
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'غير محدد';
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDomainStats = () => {
    const domains = tablesInfo.reduce((acc, table) => {
      acc[table.domain] = (acc[table.domain] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(domains)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);
  };

  const getTotalRows = () => {
    return tablesInfo.reduce((total, table) => total + (table.rowCount || 0), 0);
  };

  const getTotalColumns = () => {
    return tablesInfo.reduce((total, table) => total + table.columnsCount, 0);
  };

  if (!agentState.isInitialized || !agentState.hasSchema) {
    return (
      <div className={cn("bg-white rounded-lg shadow-md p-6", className)}>
        <div className="text-center text-gray-500">
          <Database className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">لا توجد قاعدة بيانات متصلة</h3>
          <p>يرجى إعداد اتصال قاعدة البيانات أولاً</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("bg-white rounded-lg shadow-md overflow-hidden", className)}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Database className="w-8 h-8" />
            <div>
              <h2 className="text-xl font-semibold">قاعدة البيانات</h2>
              <p className="text-blue-100">
                {agentState.databaseType?.toUpperCase()} • {agentState.totalTables} جدول
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {onRefresh && (
              <button
                onClick={onRefresh}
                className="p-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
                title="تحديث البيانات"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            )}
            {onSettings && (
              <button
                onClick={onSettings}
                className="p-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
                title="الإعدادات"
              >
                <Settings className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="p-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{agentState.totalTables}</div>
            <div className="text-sm text-blue-700">الجداول</div>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{getTotalColumns().toLocaleString('ar-SA')}</div>
            <div className="text-sm text-green-700">الأعمدة</div>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{getTotalRows().toLocaleString('ar-SA')}</div>
            <div className="text-sm text-yellow-700">السجلات</div>
          </div>
          
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{getDomainStats().length}</div>
            <div className="text-sm text-purple-700">المجالات</div>
          </div>
        </div>

        {/* Last Updated */}
        <div className="flex items-center gap-2 text-sm text-gray-600 mb-6">
          <Calendar className="w-4 h-4" />
          <span>آخر تحديث: {formatDate(agentState.schemaLastUpdated)}</span>
        </div>

        {/* Domain Distribution */}
        {getDomainStats().length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">توزيع المجالات</h3>
            <div className="space-y-2">
              {getDomainStats().map(([domain, count], index) => (
                <div key={`domain-${domain}-${index}`} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{domain}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(count / agentState.totalTables) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Tables List */}
        {tablesInfo.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">الجداول</h3>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {tablesInfo.map((table, index) => (
                <div
                  key={`table-${table.name}-${index}`}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <Table className="w-4 h-4 text-gray-500" />
                    <div>
                      <div className="font-medium text-gray-900">{table.name}</div>
                      <div className="text-sm text-gray-600 truncate max-w-xs">
                        {table.description}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {table.columnsCount} عمود
                    </div>
                    {table.rowCount !== undefined && (
                      <div className="text-xs text-gray-500">
                        {table.rowCount.toLocaleString('ar-SA')} سجل
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
