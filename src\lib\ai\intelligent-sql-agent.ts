// وكيل ذكي حقيقي لتوليد SQL باستخدام LLM فقط

import { ContextUnderstanding } from './context-understanding';
import { IntelligentSummaryGenerator } from './intelligent-summary';

export interface DatabaseSchema {
  tables: TableSchema[];
}

export interface TableSchema {
  name: string;
  description: string;
  columns: ColumnSchema[];
  relationships: RelationshipSchema[];
}

export interface ColumnSchema {
  name: string;
  type: string;
  description: string;
  nullable: boolean;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
}

export interface RelationshipSchema {
  fromTable: string;
  fromColumn: string;
  toTable: string;
  toColumn: string;
  type: 'one-to-one' | 'one-to-many' | 'many-to-many';
}

export interface SQLResult {
  query: string;
  explanation: string;
  confidence: number;
  reasoning: string[];
}

/**
 * وكيل ذكي لتحليل الأسئلة وتوليد SQL
 * يعتمد على فهم عميق لبنية قاعدة البيانات والسياق اللغوي
 */
export class IntelligentSQLAgent {
  private schema: DatabaseSchema;
  private llmClient: any; // سيتم حقن LLM client
  private contextUnderstanding: ContextUnderstanding;
  private summaryGenerator: IntelligentSummaryGenerator;

  constructor(schema: DatabaseSchema, llmClient: any) {
    this.schema = schema;
    this.llmClient = llmClient;
    this.contextUnderstanding = new ContextUnderstanding(llmClient);
    this.summaryGenerator = new IntelligentSummaryGenerator(llmClient);

    // تحديث قائمة المنتجات المتاحة
    this.updateAvailableItems();
  }

  /**
   * تحديث قائمة المنتجات المتاحة
   */
  private updateAvailableItems(): void {
    const itemsTable = this.schema.tables.find(table =>
      table.name.toLowerCase().includes('item') ||
      table.name.toLowerCase().includes('product') ||
      table.name.toLowerCase().includes('منتج')
    );

    if (itemsTable) {
      // في التطبيق الحقيقي، سيتم جلب البيانات من قاعدة البيانات
      // هنا نستخدم قائمة افتراضية للاختبار
      const sampleItems = [
        'برتقال', 'تفاح', 'موز', 'فلفل أخضر', 'طماطم', 'خيار', 'جزر',
        'لحم بط', 'لحم بقري', 'دجاج', 'سمك', 'حليب', 'جبن', 'زبدة'
      ];
      this.contextUnderstanding.updateAvailableItems(sampleItems);
    }
  }

  /**
   * تحليل سؤال المستخدم وتوليد SQL ذكي (محسن للسرعة)
   */
  async generateSQL(userQuestion: string): Promise<SQLResult> {
    console.log('🧠 بدء التحليل الذكي للسؤال:', userQuestion);

    // المرحلة 1: فهم السياق وتحسين السؤال (مدمجة)
    const contextualQuery = await this.contextUnderstanding.analyzeContext(userQuestion);
    const enhancedQuestion = contextualQuery.enhancedQuery;

    console.log('🔍 السؤال المحسن:', enhancedQuestion);

    // المرحلة 2: تحليل شامل مدمج (دمج المراحل 1-3)
    const comprehensiveAnalysis = await this.performComprehensiveAnalysis(enhancedQuestion);

    // المرحلة 3: توليد SQL مباشر (دمج المراحل 4-6)
    const finalQuery = await this.generateOptimizedSQL(enhancedQuestion, comprehensiveAnalysis);

    return {
      query: finalQuery.sql,
      explanation: finalQuery.explanation,
      confidence: finalQuery.confidence,
      reasoning: finalQuery.reasoning
    };
  }

  /**
   * تحليل شامل مدمج (يدمج التحليل اللغوي واستخراج الكيانات والربط)
   */
  private async performComprehensiveAnalysis(question: string): Promise<any> {
    const schemaDescription = this.generateSchemaDescription();

    const prompt = `
🧠 **مهمة: تحليل شامل للسؤال وربطه بقاعدة البيانات**

**السؤال:** "${question}"

**بنية قاعدة البيانات:**
${schemaDescription}

قم بتحليل السؤال وتحديد:

1. **الكيانات المطلوبة:** ما هي الجداول والأعمدة المطلوبة؟
2. **العلاقات:** كيف ترتبط الجداول ببعضها؟
3. **المرشحات:** ما هي الشروط المطلوبة؟
4. **التجميع:** هل يحتاج تجميع أو ترتيب؟
5. **نوع الاستعلام:** SELECT, COUNT, SUM, etc.

أرجع النتيجة في JSON:
{
  "requiredTables": ["table1", "table2"],
  "requiredColumns": ["col1", "col2"],
  "filters": [{"column": "col1", "condition": "LIKE", "value": "%value%"}],
  "groupBy": ["col1"],
  "orderBy": [{"column": "col2", "direction": "DESC"}],
  "queryType": "SELECT",
  "joins": [{"from": "table1", "to": "table2", "on": "table1.id = table2.table1_id"}]
}
`;

    try {
      const response = await this.llmClient.generateContent(prompt);
      return this.parseJSONResponse(response);
    } catch (error) {
      console.error('خطأ في التحليل الشامل:', error);
      return {
        requiredTables: ["items", "invoice_details", "invoices"],
        requiredColumns: ["item_name", "quantity"],
        filters: [],
        groupBy: ["item_name"],
        orderBy: [{"column": "quantity", "direction": "DESC"}],
        queryType: "SELECT",
        joins: [
          {"from": "items", "to": "invoice_details", "on": "items.item_id = invoice_details.item_id"},
          {"from": "invoice_details", "to": "invoices", "on": "invoice_details.invoice_id = invoices.invoice_id"}
        ]
      };
    }
  }

  /**
   * توليد SQL محسن مباشر
   */
  private async generateOptimizedSQL(question: string, analysis: any): Promise<any> {
    const prompt = `
⚡ **مهمة: توليد استعلام SQL محسن**

**السؤال:** "${question}"
**التحليل:** ${JSON.stringify(analysis, null, 2)}

بناءً على التحليل، اكتب استعلام SQL محسن ودقيق:

**قواعد مهمة:**
- استخدم أسماء الجداول والأعمدة الصحيحة
- أضف JOIN المناسب للعلاقات
- استخدم WHERE للمرشحات
- أضف GROUP BY و ORDER BY حسب الحاجة
- تأكد من صحة الصيغة

أرجع النتيجة في JSON:
{
  "sql": "SELECT ... FROM ... WHERE ...",
  "explanation": "شرح واضح للاستعلام",
  "confidence": 0.9,
  "reasoning": "سبب اختيار هذا الاستعلام"
}
`;

    try {
      const response = await this.llmClient.generateContent(prompt);
      return this.parseJSONResponse(response);
    } catch (error) {
      console.error('خطأ في توليد SQL:', error);
      return {
        sql: "SELECT item_name, SUM(quantity) as total_quantity FROM items INNER JOIN invoice_details ON items.item_id = invoice_details.item_id GROUP BY item_name ORDER BY total_quantity DESC",
        explanation: "استعلام افتراضي لعرض المنتجات الأكثر مبيعاً",
        confidence: 0.6,
        reasoning: "تم استخدام استعلام افتراضي بسبب مشكلة في API"
      };
    }
  }

  /**
   * توليد ملخص ذكي للنتائج
   */
  async generateIntelligentSummary(
    data: any[],
    originalQuery: string,
    context?: any
  ): Promise<any> {
    console.log('📊 توليد ملخص ذكي للنتائج...');

    return await this.summaryGenerator.generateIntelligentSummary(
      data,
      originalQuery,
      context
    );
  }

  /**
   * تحليل لغوي عميق للسؤال
   */
  private async analyzeLinguistically(question: string): Promise<any> {
    const prompt = `
🧠 **مهمة: تحليل لغوي عميق**

**السؤال:** "${question}"

قم بتحليل السؤال لغوياً وحدد:

1. **نوع السؤال:**
   - استعلام بيانات (SELECT)
   - إحصائيات (COUNT, SUM, AVG)
   - ترتيب (ORDER BY)
   - تصفية (WHERE)
   - تجميع (GROUP BY)

2. **الكيانات المذكورة:**
   - أسماء منتجات/أشخاص/أماكن
   - تواريخ وفترات زمنية
   - أرقام وكميات
   - صفات ومعايير

3. **العلاقات المطلوبة:**
   - ما الجداول التي قد تحتاج ربط؟
   - ما نوع العلاقة المطلوبة؟

4. **المخرجات المطلوبة:**
   - ما البيانات التي يريد المستخدم رؤيتها؟

أرجع النتيجة في JSON:
{
  "questionType": "...",
  "entities": [...],
  "relationships": [...],
  "expectedOutput": "..."
}
`;

    const response = await this.llmClient.generateContent(prompt);
    return this.parseJSONResponse(response);
  }

  /**
   * استخراج الكيانات من السؤال
   */
  private async extractEntities(question: string, analysis: any): Promise<any> {
    const prompt = `
🎯 **مهمة: استخراج الكيانات**

**السؤال:** "${question}"
**التحليل الأولي:** ${JSON.stringify(analysis)}

استخرج الكيانات التالية:

1. **منتجات/عناصر:** أي أسماء منتجات أو عناصر مذكورة
2. **أشخاص/عملاء:** أي أسماء أشخاص أو عملاء
3. **تواريخ:** أي تواريخ أو فترات زمنية (أشهر، سنوات، أيام)
4. **كميات/أرقام:** أي أرقام أو كميات مذكورة
5. **معايير:** أي شروط أو معايير للتصفية
6. **إجراءات:** ما نوع العملية المطلوبة (مجموع، عدد، متوسط، إلخ)

أرجع النتيجة في JSON:
{
  "products": [...],
  "people": [...],
  "dates": [...],
  "quantities": [...],
  "criteria": [...],
  "operations": [...]
}
`;

    const response = await this.llmClient.generateContent(prompt);
    return this.parseJSONResponse(response);
  }

  /**
   * ربط الكيانات بالجداول والأعمدة
   */
  private async mapEntitiesToSchema(entities: any): Promise<any> {
    const schemaDescription = this.generateSchemaDescription();
    
    const prompt = `
🔗 **مهمة: ربط الكيانات بقاعدة البيانات**

**الكيانات المستخرجة:** ${JSON.stringify(entities)}

**بنية قاعدة البيانات:**
${schemaDescription}

حدد لكل كيان:
1. **الجدول المناسب:** أي جدول يحتوي على هذا الكيان؟
2. **العمود المناسب:** أي عمود في الجدول؟
3. **نوع المطابقة:** مطابقة تامة أم جزئية (LIKE)؟
4. **الجداول المرتبطة:** أي جداول أخرى نحتاج ربطها؟

أرجع النتيجة في JSON:
{
  "tableMappings": [
    {
      "entity": "...",
      "table": "...",
      "column": "...",
      "matchType": "exact|partial",
      "relatedTables": [...]
    }
  ],
  "requiredJoins": [...]
}
`;

    const response = await this.llmClient.generateContent(prompt);
    return this.parseJSONResponse(response);
  }

  /**
   * بناء استراتيجية الاستعلام
   */
  private async buildQueryStrategy(question: string, mapping: any): Promise<any> {
    const prompt = `
📋 **مهمة: بناء استراتيجية الاستعلام**

**السؤال الأصلي:** "${question}"
**ربط الكيانات:** ${JSON.stringify(mapping)}

صمم استراتيجية الاستعلام:

1. **الجداول الأساسية:** ما الجداول التي نحتاجها؟
2. **نوع الـ JOINs:** كيف نربط الجداول؟
3. **شروط WHERE:** ما الشروط المطلوبة؟
4. **التجميع GROUP BY:** هل نحتاج تجميع؟
5. **الترتيب ORDER BY:** كيف نرتب النتائج؟
6. **الحدود LIMIT:** هل نحدد عدد النتائج؟

أرجع النتيجة في JSON:
{
  "mainTables": [...],
  "joins": [...],
  "whereConditions": [...],
  "groupBy": [...],
  "orderBy": [...],
  "limit": null
}
`;

    const response = await this.llmClient.generateContent(prompt);
    return this.parseJSONResponse(response);
  }

  /**
   * توليد SQL النهائي
   */
  private async generateSQLQuery(strategy: any): Promise<any> {
    const prompt = `
⚡ **مهمة: توليد SQL النهائي**

**استراتيجية الاستعلام:** ${JSON.stringify(strategy)}

اكتب استعلام SQL كامل وصحيح بناءً على الاستراتيجية:

1. استخدم أسماء الجداول والأعمدة الصحيحة
2. اكتب JOINs صحيحة
3. استخدم شروط WHERE مناسبة
4. طبق GROUP BY و ORDER BY حسب الحاجة
5. تأكد من صحة الـ syntax

أرجع النتيجة في JSON:
{
  "sql": "...",
  "explanation": "...",
  "steps": [...]
}
`;

    const response = await this.llmClient.generateContent(prompt);
    return this.parseJSONResponse(response);
  }

  /**
   * تحسين الاستعلام
   */
  private async optimizeQuery(query: any): Promise<any> {
    // تحسينات إضافية وتحقق من الصحة
    return {
      sql: query.sql,
      explanation: query.explanation,
      confidence: 0.9,
      reasoning: query.steps || []
    };
  }

  /**
   * توليد وصف مفصل لبنية قاعدة البيانات
   */
  private generateSchemaDescription(): string {
    let description = "📊 **بنية قاعدة البيانات:**\n\n";
    
    for (const table of this.schema.tables) {
      description += `**جدول: ${table.name}**\n`;
      description += `الوصف: ${table.description}\n`;
      description += `الأعمدة:\n`;
      
      for (const column of table.columns) {
        description += `  - ${column.name} (${column.type}): ${column.description}\n`;
      }
      
      if (table.relationships.length > 0) {
        description += `العلاقات:\n`;
        for (const rel of table.relationships) {
          description += `  - ${rel.fromColumn} → ${rel.toTable}.${rel.toColumn} (${rel.type})\n`;
        }
      }
      
      description += "\n";
    }
    
    return description;
  }

  /**
   * تحليل استجابة JSON من LLM مع التعامل مع التنسيقات المختلفة
   */
  private parseJSONResponse(response: string): any {
    try {
      // محاولة تحليل مباشر
      return JSON.parse(response);
    } catch (error) {
      // إزالة markdown code blocks
      let cleanResponse = response.trim();

      // إزالة ```json و ```
      cleanResponse = cleanResponse.replace(/^```json\s*/i, '');
      cleanResponse = cleanResponse.replace(/^```\s*/i, '');
      cleanResponse = cleanResponse.replace(/\s*```$/i, '');

      // إزالة أي نص قبل أو بعد JSON
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }

      try {
        return JSON.parse(cleanResponse);
      } catch (secondError) {
        console.error('فشل في تحليل JSON:', response);
        console.error('النص المنظف:', cleanResponse);
        throw new Error(`فشل في تحليل استجابة JSON: ${secondError}`);
      }
    }
  }
}
