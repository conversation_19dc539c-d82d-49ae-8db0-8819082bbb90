// نظام فهم السياق اللغوي والمرادفات

export interface ContextMatch {
  originalTerm: string;
  matchedTerm: string;
  confidence: number;
  type: 'exact' | 'partial' | 'semantic' | 'normalized';
}

export interface ContextualQuery {
  originalQuery: string;
  enhancedQuery: string;
  matches: ContextMatch[];
  suggestions: string[];
}

/**
 * نظام ذكي لفهم السياق اللغوي والمرادفات
 */
export class ContextUnderstanding {
  private llmClient: any;
  private availableItems: string[] = [];

  constructor(llmClient: any) {
    this.llmClient = llmClient;
  }

  /**
   * تحديث قائمة المنتجات المتاحة
   */
  updateAvailableItems(items: string[]): void {
    this.availableItems = items;
  }

  /**
   * تحليل السؤال وفهم السياق
   */
  async analyzeContext(userQuestion: string): Promise<ContextualQuery> {
    console.log('🧠 تحليل السياق اللغوي للسؤال:', userQuestion);

    // استخراج المصطلحات المحتملة
    const extractedTerms = this.extractPotentialTerms(userQuestion);
    
    // البحث عن تطابقات ذكية
    const matches: ContextMatch[] = [];
    
    for (const term of extractedTerms) {
      const contextMatches = await this.findContextualMatches(term);
      matches.push(...contextMatches);
    }

    // تحسين الاستعلام
    const enhancedQuery = await this.enhanceQuery(userQuestion, matches);

    return {
      originalQuery: userQuestion,
      enhancedQuery,
      matches,
      suggestions: this.generateSuggestions(matches)
    };
  }

  /**
   * استخراج المصطلحات المحتملة من السؤال
   */
  private extractPotentialTerms(question: string): string[] {
    const terms: string[] = [];
    
    // إزالة كلمات الربط والأدوات
    const stopWords = ['ال', 'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك'];
    
    // تقسيم النص إلى كلمات
    const words = question.split(/\s+/);
    
    // استخراج المصطلحات المحتملة
    for (let i = 0; i < words.length; i++) {
      const word = words[i].trim();
      
      if (word.length > 2 && !stopWords.includes(word)) {
        terms.push(word);
        
        // محاولة دمج كلمتين متتاليتين
        if (i < words.length - 1) {
          const nextWord = words[i + 1].trim();
          if (nextWord.length > 2 && !stopWords.includes(nextWord)) {
            terms.push(`${word} ${nextWord}`);
          }
        }
        
        // محاولة دمج ثلاث كلمات
        if (i < words.length - 2) {
          const nextWord = words[i + 1].trim();
          const thirdWord = words[i + 2].trim();
          if (nextWord.length > 2 && thirdWord.length > 2) {
            terms.push(`${word} ${nextWord} ${thirdWord}`);
          }
        }
      }
    }
    
    return terms;
  }

  /**
   * البحث عن تطابقات سياقية ذكية
   */
  private async findContextualMatches(term: string): Promise<ContextMatch[]> {
    const matches: ContextMatch[] = [];
    
    // 1. تطابق مباشر
    const exactMatch = this.findExactMatch(term);
    if (exactMatch) {
      matches.push(exactMatch);
    }

    // 2. تطابق جزئي
    const partialMatches = this.findPartialMatches(term);
    matches.push(...partialMatches);

    // 3. تطابق دلالي باستخدام AI
    const semanticMatches = await this.findSemanticMatches(term);
    matches.push(...semanticMatches);

    // 4. تطابق بعد التطبيع
    const normalizedMatches = this.findNormalizedMatches(term);
    matches.push(...normalizedMatches);

    return matches;
  }

  /**
   * البحث عن تطابق مباشر
   */
  private findExactMatch(term: string): ContextMatch | null {
    const exactMatch = this.availableItems.find(item => 
      item.toLowerCase() === term.toLowerCase()
    );

    if (exactMatch) {
      return {
        originalTerm: term,
        matchedTerm: exactMatch,
        confidence: 1.0,
        type: 'exact'
      };
    }

    return null;
  }

  /**
   * البحث عن تطابقات جزئية
   */
  private findPartialMatches(term: string): ContextMatch[] {
    const matches: ContextMatch[] = [];
    const termLower = term.toLowerCase();

    for (const item of this.availableItems) {
      const itemLower = item.toLowerCase();
      
      // تطابق جزئي في أي اتجاه
      if (itemLower.includes(termLower) || termLower.includes(itemLower)) {
        const confidence = Math.max(
          termLower.length / itemLower.length,
          itemLower.length / termLower.length
        ) * 0.8; // تقليل الثقة للتطابق الجزئي

        matches.push({
          originalTerm: term,
          matchedTerm: item,
          confidence,
          type: 'partial'
        });
      }
    }

    return matches.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * البحث عن تطابقات دلالية باستخدام AI
   */
  private async findSemanticMatches(term: string): Promise<ContextMatch[]> {
    if (this.availableItems.length === 0) return [];

    try {
      const prompt = `
🧠 **مهمة: البحث عن المرادفات والتطابقات الدلالية**

**المصطلح المطلوب:** "${term}"

**المنتجات المتاحة:**
${this.availableItems.slice(0, 20).map((item, index) => `${index + 1}. ${item}`).join('\n')}

حدد أي من المنتجات المتاحة يمكن أن يكون مرادفاً أو مطابقاً دلالياً للمصطلح المطلوب.

مثال:
- "البرتقال" يطابق "برتقال"
- "الفلفل الأخضر" يطابق "فلفل أخضر"
- "التفاح الأحمر" يطابق "تفاح"

أرجع النتيجة في JSON:
{
  "matches": [
    {
      "item": "اسم المنتج المطابق",
      "confidence": 0.9,
      "reason": "سبب التطابق"
    }
  ]
}
`;

      const response = await this.llmClient.generateContent(prompt);
      const result = this.parseJSONResponse(response);
      
      return result.matches?.map((match: any) => ({
        originalTerm: term,
        matchedTerm: match.item,
        confidence: match.confidence * 0.7, // تقليل الثقة للتطابق الدلالي
        type: 'semantic' as const
      })) || [];

    } catch (error) {
      console.error('خطأ في البحث الدلالي:', error);
      return [];
    }
  }

  /**
   * البحث بعد تطبيع النص
   */
  private findNormalizedMatches(term: string): ContextMatch[] {
    const matches: ContextMatch[] = [];
    
    // تطبيع النص (إزالة ال التعريف، تنظيف الأحرف)
    const normalizedTerm = this.normalizeText(term);
    
    for (const item of this.availableItems) {
      const normalizedItem = this.normalizeText(item);
      
      if (normalizedItem === normalizedTerm) {
        matches.push({
          originalTerm: term,
          matchedTerm: item,
          confidence: 0.9,
          type: 'normalized'
        });
      }
    }

    return matches;
  }

  /**
   * تطبيع النص
   */
  private normalizeText(text: string): string {
    return text
      .toLowerCase()
      .replace(/^ال/, '') // إزالة ال التعريف من البداية
      .replace(/[أإآ]/g, 'ا') // توحيد الألف
      .replace(/[ىي]/g, 'ي') // توحيد الياء
      .replace(/ة/g, 'ه') // توحيد التاء المربوطة
      .replace(/[^\u0600-\u06FF\s]/g, '') // إزالة الأحرف غير العربية
      .trim();
  }

  /**
   * تحسين الاستعلام بناءً على التطابقات
   */
  private async enhanceQuery(originalQuery: string, matches: ContextMatch[]): Promise<string> {
    if (matches.length === 0) return originalQuery;

    // اختيار أفضل تطابق
    const bestMatch = matches.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    );

    // استبدال المصطلح في الاستعلام
    let enhancedQuery = originalQuery;
    
    if (bestMatch.confidence > 0.7) {
      enhancedQuery = originalQuery.replace(
        new RegExp(bestMatch.originalTerm, 'gi'),
        bestMatch.matchedTerm
      );
    }

    return enhancedQuery;
  }

  /**
   * توليد اقتراحات للمستخدم
   */
  private generateSuggestions(matches: ContextMatch[]): string[] {
    return matches
      .filter(match => match.confidence > 0.5)
      .slice(0, 5)
      .map(match => match.matchedTerm);
  }

  /**
   * تحليل استجابة JSON من LLM
   */
  private parseJSONResponse(response: string): any {
    try {
      return JSON.parse(response);
    } catch (error) {
      // إزالة markdown code blocks
      let cleanResponse = response.trim();
      cleanResponse = cleanResponse.replace(/^```json\s*/i, '');
      cleanResponse = cleanResponse.replace(/^```\s*/i, '');
      cleanResponse = cleanResponse.replace(/\s*```$/i, '');
      
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }
      
      try {
        return JSON.parse(cleanResponse);
      } catch (secondError) {
        console.error('فشل في تحليل JSON:', response);
        return { matches: [] };
      }
    }
  }
}
