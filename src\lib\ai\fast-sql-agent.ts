// وكيل SQL محسن للسرعة - يقلل عدد استدعاءات API

import { GoogleGeminiClient } from './google-gemini-client';
import { IntelligentSummaryGenerator } from './intelligent-summary';
import { ContextUnderstanding } from './context-understanding';

export interface SQLResult {
  query: string;
  explanation: string;
  confidence: number;
  reasoning?: string;
}

export class FastSQLAgent {
  private llmClient: GoogleGeminiClient;
  private summaryGenerator: IntelligentSummaryGenerator;
  private contextUnderstanding: ContextUnderstanding;
  private schema: any;

  constructor(schema: any, llmClient: GoogleGeminiClient) {
    this.schema = schema;
    this.llmClient = llmClient;
    this.summaryGenerator = new IntelligentSummaryGenerator(llmClient);
    this.contextUnderstanding = new ContextUnderstanding(llmClient);
  }

  /**
   * توليد SQL بطريقة سريعة (استدعاء واحد فقط)
   */
  async generateSQL(userQuestion: string): Promise<SQLResult> {
    console.log('⚡ بدء التحليل السريع للسؤال:', userQuestion);

    // فحص إذا كان السؤال يتضمن مقارنة
    const isComparison = this.detectComparison(userQuestion);
    const comparisonItems = isComparison ? this.extractComparisonItems(userQuestion) : [];

    // تحسين السياق أولاً (استدعاء واحد)
    const contextualQuery = await this.contextUnderstanding.analyzeContext(userQuestion);
    const enhancedQuestion = contextualQuery.enhancedQuery;

    console.log('🔍 السؤال المحسن:', enhancedQuestion);
    if (isComparison) {
      console.log('🔄 تم اكتشاف مقارنة - العناصر:', comparisonItems);
    }

    // توليد SQL مباشر في استدعاء واحد
    let result: SQLResult;

    if (isComparison && comparisonItems.length > 0) {
      // محاولة حل سريع للمقارنات
      result = await this.generateComparisonSQL(enhancedQuestion, comparisonItems);
    } else {
      result = await this.generateSQLDirect(enhancedQuestion, isComparison, comparisonItems);
    }

    return result;
  }

  /**
   * اكتشاف إذا كان السؤال يتضمن مقارنة
   */
  private detectComparison(question: string): boolean {
    const comparisonKeywords = [
      'قارن', 'مقارنة', 'بين', 'مقابل', 'ضد', 'أفضل من', 'أكثر من', 'أقل من',
      'الفرق بين', 'الاختلاف', 'التباين', 'versus', 'vs', 'compare'
    ];

    return comparisonKeywords.some(keyword =>
      question.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * استخراج العناصر المراد مقارنتها من السؤال
   */
  private extractComparisonItems(question: string): string[] {
    const items: string[] = [];

    // البحث عن أنماط شائعة
    const patterns = [
      /بين\s+(.+?)\s+و\s*(.+?)(?:\s|$)/g,  // "بين X و Y"
      /(.+?)\s+مقابل\s+(.+?)(?:\s|$)/g,     // "X مقابل Y"
      /(.+?)\s+ضد\s+(.+?)(?:\s|$)/g,        // "X ضد Y"
      /مقارنة\s+(.+?)\s+و\s*(.+?)(?:\s|$)/g // "مقارنة X و Y"
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(question)) !== null) {
        if (match[1]) {
          // تنظيف العنصر الأول (إزالة "مبيعات" لكن الاحتفاظ بـ "ال")
          let item1 = match[1].trim().replace(/مبيعات\s+/g, '');
          items.push(item1);
        }
        if (match[2]) {
          // تنظيف العنصر الثاني (إزالة "مبيعات" لكن الاحتفاظ بـ "ال")
          let item2 = match[2].trim().replace(/مبيعات\s+/g, '');
          items.push(item2);
        }
      }
    }

    return items.filter(item => item.length > 0);
  }

  /**
   * توليد SQL خاص للمقارنات (حل سريع)
   */
  private async generateComparisonSQL(_question: string, comparisonItems: string[]): Promise<SQLResult> {
    console.log('🔄 توليد استعلام مقارنة سريع للعناصر:', comparisonItems);

    // بناء شروط دقيقة لكل عنصر
    const itemConditions: string[] = [];

    comparisonItems.forEach(item => {
      // تنظيف العنصر (إزالة كلمات غير مهمة لكن الاحتفاظ بـ "ال" للمعالجة اللاحقة)
      const cleanItem = item.replace(/\b(مبيعات|عميل|منتج)\b/g, '').trim();

      // إذا كان العنصر يحتوي على "لحم" مع نوع محدد
      if (cleanItem.includes('لحم')) {
        let meatType = cleanItem.replace('لحم', '').trim();
        // إزالة أدوات التعريف
        meatType = meatType.replace(/^ال/, '');

        if (meatType) {
          // معالجة خاصة لـ "البط" -> "بط"
          if (meatType === 'البط') {
            meatType = 'بط';
          }

          // البحث عن النوع المحدد من اللحم باستخدام الدالة الذكية
          const meatCondition = this.createSmartLikeCondition('i.item_name', meatType);
          itemConditions.push(`(i.item_name LIKE '%لحم%' AND ${meatCondition})`);
        } else {
          // البحث عن أي لحم
          itemConditions.push(`i.item_name LIKE '%لحم%'`);
        }
      } else {
        // البحث عن المنتج مباشرة باستخدام الدالة الذكية
        const productCondition = this.createSmartLikeCondition('i.item_name', cleanItem);
        itemConditions.push(productCondition);
      }
    });

    console.log('🔍 شروط البحث المولدة:', itemConditions);

    const whereClause = itemConditions.length > 0 ? itemConditions.join(' OR ') : "1=1";

    const query = `SELECT
      i.item_name,
      SUM(id.quantity) as total_quantity
    FROM items i
    INNER JOIN invoice_details id ON i.item_id = id.item_id
    WHERE ${whereClause}
    GROUP BY i.item_name
    ORDER BY total_quantity DESC`;

    return {
      query,
      explanation: 'مقارنة مبيعات',
      confidence: 0.9,
      reasoning: 'استعلام مقارنة'
    };
  }

  /**
   * توليد SQL مباشر في استدعاء واحد
   */
  private async generateSQLDirect(question: string, isComparison: boolean = false, comparisonItems: string[] = []): Promise<SQLResult> {
    const schemaDescription = this.generateSchemaDescription();
    
    const prompt = `SQL للسؤال: "${question}"

الجداول:
${schemaDescription}

قواعد مهمة:
- للمقارنات: استخدم LIKE '%نص%'
- للمبيعات: items → invoice_details (وليس products)
- للعملاء: customers → invoices
- ⚠️ جدول products لا يرتبط بالمبيعات - استخدم items دائماً
- للمقارنات بين المنتجات: SELECT i.item_name, SUM(id.quantity) FROM items i JOIN invoice_details id

JSON فقط:
{"query": "SELECT ..."}`;

    try {
      const response = await this.llmClient.generateContent(prompt);
      const result = this.parseJSONResponse(response);
      
      // التحقق من صحة النتيجة
      if (!result.query) {
        throw new Error('لم يتم الحصول على استعلام من AI');
      }

      return {
        query: result.query,
        explanation: 'استعلام SQL',
        confidence: 0.9,
        reasoning: 'تم توليد الاستعلام'
      };

    } catch (error) {
      console.error('خطأ في توليد SQL السريع:', error);

      // استعلام افتراضي حسب نوع السؤال
      if (isComparison) {
        // بناء شروط البحث من العناصر المستخرجة باستخدام الدالة الذكية
        let whereConditions = '';
        if (comparisonItems.length > 0) {
          const conditions = comparisonItems.map(item => {
            // تنظيف العنصر
            const cleanItem = item.replace(/\b(مبيعات|عميل|منتج)\b/g, '').trim();
            return this.createSmartLikeCondition('i.item_name', cleanItem);
          }).join(' OR ');
          whereConditions = `WHERE ${conditions}`;
        } else {
          // افتراضي للحوم (مع معالجة "ال")
          const duckCondition = this.createSmartLikeCondition('i.item_name', 'بط');
          const turkeyCondition = this.createSmartLikeCondition('i.item_name', 'ديك رومي');
          whereConditions = `WHERE ${duckCondition} OR ${turkeyCondition}`;
        }

        return {
          query: `SELECT
            i.item_name,
            SUM(id.quantity) as total_quantity
          FROM items i
          INNER JOIN invoice_details id ON i.item_id = id.item_id
          ${whereConditions}
          GROUP BY i.item_name
          ORDER BY total_quantity DESC`,
          explanation: 'مقارنة مبيعات',
          confidence: 0.8,
          reasoning: 'استعلام مقارنة'
        };
      } else {
        // استعلام افتراضي عام
        return {
          query: `SELECT
            i.item_name,
            SUM(id.quantity) as total_quantity
          FROM items i
          INNER JOIN invoice_details id ON i.item_id = id.item_id
          INNER JOIN invoices inv ON id.invoice_id = inv.invoice_id
          GROUP BY i.item_name
          ORDER BY total_quantity DESC`,
          explanation: 'المنتجات الأكثر مبيعاً',
          confidence: 0.7,
          reasoning: 'استعلام افتراضي'
        };
      }
    }
  }

  /**
   * إنشاء شرط LIKE ذكي يتعامل مع أداة التعريف "ال"
   */
  private createSmartLikeCondition(columnName: string, searchTerm: string): string {
    const cleanTerm = searchTerm.trim();

    // إذا كان المصطلح يبدأ بـ "ال"
    if (cleanTerm.startsWith('ال')) {
      const withoutAl = cleanTerm.substring(2); // إزالة "ال"
      return `(${columnName} LIKE '%${cleanTerm}%' OR ${columnName} LIKE '%${withoutAl}%')`;
    } else {
      // إذا لم يبدأ بـ "ال"، ابحث عن الاثنين
      return `(${columnName} LIKE '%${cleanTerm}%' OR ${columnName} LIKE '%ال${cleanTerm}%')`;
    }
  }

  /**
   * توليد وصف مبسط لبنية قاعدة البيانات
   */
  private generateSchemaDescription(): string {
    if (!this.schema || !this.schema.tables) {
      return 'items, invoice_details, invoices, customers';
    }

    // وصف مبسط جداً لتوفير نقاط API
    const tableNames = this.schema.tables.map((table: any) => table.name).join(', ');
    return tableNames;
  }

  /**
   * تحليل استجابة JSON مبسط
   */
  private parseJSONResponse(response: string): any {
    try {
      return JSON.parse(response);
    } catch {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch {
          // فشل
        }
      }
      return { query: "SELECT * FROM items LIMIT 10" };
    }
  }

  /**
   * توليد ملخص ذكي للنتائج
   */
  async generateIntelligentSummary(
    data: any[],
    originalQuery: string,
    context?: any
  ): Promise<any> {
    console.log('📊 توليد ملخص ذكي للنتائج...');

    return await this.summaryGenerator.generateIntelligentSummary(
      data,
      originalQuery,
      context
    );
  }

  /**
   * تحديث بنية قاعدة البيانات
   */
  updateSchema(newSchema: any): void {
    this.schema = newSchema;
    console.log('✅ تم تحديث بنية قاعدة البيانات');
  }
}
