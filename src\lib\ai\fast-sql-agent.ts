// وكيل SQL محسن للسرعة - يقلل عدد استدعاءات API

import { GoogleGeminiClient } from './google-gemini-client';
import { IntelligentSummaryGenerator } from './intelligent-summary';
import { ContextUnderstanding } from './context-understanding';

export interface SQLResult {
  query: string;
  explanation: string;
  confidence: number;
  reasoning?: string;
}

export class FastSQLAgent {
  private llmClient: GoogleGeminiClient;
  private summaryGenerator: IntelligentSummaryGenerator;
  private contextUnderstanding: ContextUnderstanding;
  private schema: any;

  constructor(schema: any, llmClient: GoogleGeminiClient) {
    this.schema = schema;
    this.llmClient = llmClient;
    this.summaryGenerator = new IntelligentSummaryGenerator(llmClient);
    this.contextUnderstanding = new ContextUnderstanding(llmClient);
  }

  /**
   * توليد SQL بطريقة سريعة (استدعاء واحد فقط)
   */
  async generateSQL(userQuestion: string): Promise<SQLResult> {
    console.log('⚡ بدء التحليل السريع للسؤال:', userQuestion);

    // فحص إذا كان السؤال يتضمن مقارنة
    const isComparison = this.detectComparison(userQuestion);
    const comparisonItems = isComparison ? this.extractComparisonItems(userQuestion) : [];

    // تحسين السياق أولاً (استدعاء واحد)
    const contextualQuery = await this.contextUnderstanding.analyzeContext(userQuestion);
    const enhancedQuestion = contextualQuery.enhancedQuery;

    console.log('🔍 السؤال المحسن:', enhancedQuestion);
    if (isComparison) {
      console.log('🔄 تم اكتشاف مقارنة - العناصر:', comparisonItems);
    }

    // توليد SQL مباشر في استدعاء واحد
    let result: SQLResult;

    if (isComparison && comparisonItems.length > 0) {
      // محاولة حل سريع للمقارنات
      result = await this.generateComparisonSQL(enhancedQuestion, comparisonItems);
    } else {
      result = await this.generateSQLDirect(enhancedQuestion, isComparison, comparisonItems);
    }

    return result;
  }

  /**
   * اكتشاف إذا كان السؤال يتضمن مقارنة
   */
  private detectComparison(question: string): boolean {
    const comparisonKeywords = [
      'قارن', 'مقارنة', 'بين', 'مقابل', 'ضد', 'أفضل من', 'أكثر من', 'أقل من',
      'الفرق بين', 'الاختلاف', 'التباين', 'versus', 'vs', 'compare'
    ];

    return comparisonKeywords.some(keyword =>
      question.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * استخراج العناصر المراد مقارنتها من السؤال
   */
  private extractComparisonItems(question: string): string[] {
    const items: string[] = [];

    // البحث عن أنماط شائعة
    const patterns = [
      /بين\s+(.+?)\s+و\s*(.+?)(?:\s|$)/g,  // "بين X و Y"
      /(.+?)\s+مقابل\s+(.+?)(?:\s|$)/g,     // "X مقابل Y"
      /(.+?)\s+ضد\s+(.+?)(?:\s|$)/g,        // "X ضد Y"
      /مقارنة\s+(.+?)\s+و\s*(.+?)(?:\s|$)/g // "مقارنة X و Y"
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(question)) !== null) {
        if (match[1]) {
          // تنظيف العنصر الأول (إزالة "مبيعات" لكن الاحتفاظ بـ "ال")
          let item1 = match[1].trim().replace(/مبيعات\s+/g, '');
          items.push(item1);
        }
        if (match[2]) {
          // تنظيف العنصر الثاني (إزالة "مبيعات" لكن الاحتفاظ بـ "ال")
          let item2 = match[2].trim().replace(/مبيعات\s+/g, '');
          items.push(item2);
        }
      }
    }

    return items.filter(item => item.length > 0);
  }

  /**
   * توليد SQL خاص للمقارنات (حل سريع) باستخدام الجداول الحقيقية
   */
  private async generateComparisonSQL(_question: string, comparisonItems: string[]): Promise<SQLResult> {
    console.log('🔄 توليد استعلام مقارنة سريع للعناصر:', comparisonItems);

    // استخدام دالة الاستعلام الاحتياطي التي تستخدم الجداول الحقيقية
    return this.generateFallbackQuery('comparison', comparisonItems);
  }

  /**
   * توليد SQL مباشر في استدعاء واحد
   */
  private async generateSQLDirect(question: string, isComparison: boolean = false, comparisonItems: string[] = []): Promise<SQLResult> {
    const schemaDescription = this.generateSchemaDescription();
    
    const prompt = `🚀 **مولد SQL سريع ومحسن**

**السؤال:** "${question}"

**قاعدة البيانات:** SQL Server (استخدم TOP بدلاً من LIMIT)

**الجداول المتاحة:**
${schemaDescription}

**قواعد مهمة:**
- استخدم أسماء الجداول والأعمدة الموجودة فعلياً فقط من القائمة أعلاه
- SQL Server: استخدم TOP بدلاً من LIMIT
- الجدول الرئيسي: tbltemp_Inv_MainInvoice (يحتوي على جميع البيانات)
- للمنتجات: استخدم ItemID من tbltemp_Inv_MainInvoice
- للعملاء: استخدم ClientID أو SupplierName من tbltemp_Inv_MainInvoice
- للكميات: استخدم Quantity من tbltemp_Inv_MainInvoice
- للمبالغ: استخدم TotalAmount من tbltemp_Inv_MainInvoice
- لا تستخدم أسماء جداول مثل items, products, customers (غير موجودة)
- لا تستخدم JOIN إلا إذا كان ضرورياً جداً

**أمثلة:**
- "أكثر المنتجات مبيعاً" → SELECT TOP 10 ItemID, SUM(Quantity) FROM tbltemp_Inv_MainInvoice GROUP BY ItemID ORDER BY SUM(Quantity) DESC
- "عدد العملاء" → SELECT COUNT(DISTINCT ClientID) FROM tbltemp_Inv_MainInvoice

JSON فقط:
{"query": "SELECT ..."}`;

    try {
      const response = await this.llmClient.generateContent(prompt);
      const result = this.parseJSONResponse(response);
      
      // التحقق من صحة النتيجة
      if (!result.query) {
        throw new Error('لم يتم الحصول على استعلام من AI');
      }

      return {
        query: result.query,
        explanation: 'استعلام SQL',
        confidence: 0.9,
        reasoning: 'تم توليد الاستعلام'
      };

    } catch (error) {
      console.error('خطأ في توليد SQL السريع:', error);

      // استعلام افتراضي حسب نوع السؤال باستخدام الجداول الحقيقية
      if (isComparison) {
        return this.generateFallbackQuery('comparison', comparisonItems);
      } else {
        return this.generateFallbackQuery('general');
      }
    }
  }

  /**
   * إنشاء شرط LIKE ذكي يتعامل مع أداة التعريف "ال"
   */
  private createSmartLikeCondition(columnName: string, searchTerm: string): string {
    const cleanTerm = searchTerm.trim();

    // إذا كان المصطلح يبدأ بـ "ال"
    if (cleanTerm.startsWith('ال')) {
      const withoutAl = cleanTerm.substring(2); // إزالة "ال"
      return `(${columnName} LIKE '%${cleanTerm}%' OR ${columnName} LIKE '%${withoutAl}%')`;
    } else {
      // إذا لم يبدأ بـ "ال"، ابحث عن الاثنين
      return `(${columnName} LIKE '%${cleanTerm}%' OR ${columnName} LIKE '%ال${cleanTerm}%')`;
    }
  }

  /**
   * توليد استعلام احتياطي باستخدام الجداول الحقيقية
   */
  public generateFallbackQuery(type: 'comparison' | 'general', _items?: string[]): SQLResult {
    if (!this.schema || !this.schema.tables || this.schema.tables.length === 0) {
      return {
        query: "SELECT 'لا توجد جداول متاحة' as message",
        explanation: 'لا توجد جداول متاحة في قاعدة البيانات',
        confidence: 0.1,
        reasoning: 'لا توجد بيانات'
      };
    }

    // البحث عن جدول الفواتير الرئيسي
    const invoiceTable = this.schema.tables.find((t: { name: string }) =>
      t.name.toLowerCase().includes('invoice') ||
      t.name.toLowerCase().includes('فاتورة') ||
      t.name.includes('Inv_MainInvoice')
    );

    if (!invoiceTable) {
      // إذا لم نجد جدول فواتير، استخدم أول جدول متاح
      const firstTable = this.schema.tables[0];
      return {
        query: `SELECT COUNT(*) as total_count FROM ${firstTable.name}`,
        explanation: `عدد السجلات في جدول ${firstTable.name}`,
        confidence: 0.5,
        reasoning: 'استعلام عام على أول جدول متاح'
      };
    }

    // البحث عن أعمدة مهمة في جدول الفواتير مع التمييز بين ID والاسم
    const quantityColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('quantity') ||
      c.name.toLowerCase().includes('كمية') ||
      c.name === 'Quantity'
    );

    // البحث عن عمود اسم المنتج أولاً (للعرض)
    const itemNameColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('itemname') ||
      c.name.toLowerCase().includes('productname') ||
      c.name.toLowerCase().includes('منتج') && c.name.toLowerCase().includes('اسم') ||
      c.name === 'ItemName'
    );

    // البحث عن عمود ID المنتج (للتجميع)
    const itemIdColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('itemid') ||
      c.name.toLowerCase().includes('productid') ||
      c.name === 'ItemID'
    );

    // البحث عن عمود اسم العميل أولاً (للعرض)
    const clientNameColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('clientname') ||
      c.name.toLowerCase().includes('customername') ||
      c.name.toLowerCase().includes('suppliername') ||
      c.name === 'ClientName' || c.name === 'SupplierName'
    );

    // البحث عن عمود ID العميل (للتجميع)
    const clientIdColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('clientid') ||
      c.name.toLowerCase().includes('customerid') ||
      c.name === 'ClientID'
    );

    const amountColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('amount') ||
      c.name.toLowerCase().includes('total') ||
      c.name.toLowerCase().includes('مبلغ') ||
      c.name === 'TotalAmount'
    );

    // توليد استعلام حسب النوع مع اختيار الأعمدة المناسبة
    if (type === 'comparison' && quantityColumn && (itemNameColumn || itemIdColumn)) {
      // للمقارنات: استخدم ItemID للتجميع والعرض (لأن ItemName قد لا يكون متوفر)
      const displayColumn = itemIdColumn || itemNameColumn;

      return {
        query: `SELECT TOP 10 ${displayColumn!.name}, SUM(${quantityColumn.name}) as total_quantity
                FROM ${invoiceTable.name}
                WHERE ${quantityColumn.name} IS NOT NULL AND ${displayColumn!.name} IS NOT NULL
                GROUP BY ${displayColumn!.name}
                ORDER BY total_quantity DESC`,
        explanation: 'أكثر المنتجات مبيعاً حسب الكمية',
        confidence: 0.8,
        reasoning: 'استعلام مقارنة باستخدام الجداول الحقيقية'
      };
    } else if (clientIdColumn) {
      // لعد العملاء: استخدم ID للدقة
      return {
        query: `SELECT COUNT(DISTINCT ${clientIdColumn.name}) as customer_count
                FROM ${invoiceTable.name}
                WHERE ${clientIdColumn.name} IS NOT NULL`,
        explanation: 'عدد العملاء المختلفين',
        confidence: 0.8,
        reasoning: 'عد العملاء باستخدام الجداول الحقيقية'
      };
    } else if (clientNameColumn && amountColumn) {
      // لأكثر العملاء شراءً: استخدم اسم العميل للعرض
      return {
        query: `SELECT TOP 10 ${clientNameColumn.name}, SUM(${amountColumn.name}) as total_amount
                FROM ${invoiceTable.name}
                WHERE ${amountColumn.name} IS NOT NULL AND ${clientNameColumn.name} IS NOT NULL
                GROUP BY ${clientNameColumn.name}
                ORDER BY total_amount DESC`,
        explanation: 'أكثر العملاء شراءً حسب المبلغ',
        confidence: 0.8,
        reasoning: 'ترتيب العملاء حسب المبيعات'
      };
    } else if (amountColumn) {
      return {
        query: `SELECT SUM(${amountColumn.name}) as total_sales
                FROM ${invoiceTable.name}
                WHERE ${amountColumn.name} IS NOT NULL`,
        explanation: 'إجمالي المبيعات',
        confidence: 0.7,
        reasoning: 'حساب إجمالي المبيعات'
      };
    } else {
      return {
        query: `SELECT COUNT(*) as total_records FROM ${invoiceTable.name}`,
        explanation: `عدد السجلات في جدول ${invoiceTable.name}`,
        confidence: 0.6,
        reasoning: 'استعلام عام'
      };
    }
  }

  /**
   * توليد وصف مبسط لبنية قاعدة البيانات
   */
  private generateSchemaDescription(): string {
    if (!this.schema || !this.schema.tables) {
      return 'لا توجد جداول متاحة';
    }

    // وصف مفصل للجداول والأعمدة المهمة
    let description = '';

    for (const table of this.schema.tables) {
      description += `\n**${table.name}:**\n`;

      // إضافة أهم الأعمدة (أول 10 أعمدة)
      const importantColumns = table.columns.slice(0, 10);
      for (const column of importantColumns) {
        description += `  - ${column.name} (${column.type})\n`;
      }
    }

    return description;
  }

  /**
   * تحليل استجابة JSON مبسط
   */
  private parseJSONResponse(response: string): any {
    try {
      return JSON.parse(response);
    } catch {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch {
          // فشل
        }
      }
      return { query: "SELECT * FROM items LIMIT 10" };
    }
  }

  /**
   * توليد ملخص ذكي للنتائج
   */
  async generateIntelligentSummary(
    data: any[],
    originalQuery: string,
    context?: any
  ): Promise<any> {
    console.log('📊 توليد ملخص ذكي للنتائج...');

    return await this.summaryGenerator.generateIntelligentSummary(
      data,
      originalQuery,
      context
    );
  }

  /**
   * تحديث بنية قاعدة البيانات
   */
  updateSchema(newSchema: any): void {
    this.schema = newSchema;
    console.log('✅ تم تحديث بنية قاعدة البيانات');
  }
}
