{"databaseName": "SalesTempDB", "databaseType": "mssql", "tables": [{"name": "tbltemp_Inv_MainInvoice", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SupplierName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "InvoiceID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DetailsID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "EnterTime", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "TotalAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MainUnitQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "MainUnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 20}, {"name": "MainUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "MCAmount", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "MainUnitBonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "ExchangePrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "TotalAmountByCurrencyInvetory", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "NewSubItemEntryID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_Inv_MainInvoice", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 908}, {"name": "tbltemp_ItemsMain", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RowVersion", "type": "timestamp", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "DocumentID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethodID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Discount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Notes", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255, "precision": null, "scale": null}, {"name": "UserID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheYear", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 10, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CategoryID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CategoryName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CategoryNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "ItemTypeID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemType", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "ReorderPoint", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 4}, {"name": "ISActive", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ISExpiry", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ExpiryPoint", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountFatherNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Account<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CostCenterNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Barcode", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitRank", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "PackageQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "BarcodeID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SerialNumber", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ItemD<PERSON>unt", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "McItemDiscountCurrencyMain", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "Mc<PERSON>temDiscount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "Amount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmountCurrencyMain", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "AccountID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "PackageUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "PackageUnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "NextParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangePrice", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExchangePriceCurrencyInvetory", "type": "decimal", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_ItemsMain", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 0}], "relationships": [], "extractedAt": "2025-07-21T21:09:48.057Z", "version": "1.0", "tableDescriptions": [{"tableName": "tbltemp_Inv_MainInvoice", "description": "جدول tbltemp_Inv_MainInvoice يحتوي على بيانات تفصيلية للفواتير الرئيسية، ويستخدم لتخزين معلومات متعلقة بالفواتير مثل تفاصيل البضائع، الموردين، العملاء، والمخازن. هذا الجدول يوفر بيانات مفصلة يمكن استخدامها في تحليلات مالية وإدارية.", "columnDescriptions": {"ID": "مفتاح رئيسي فريد لكل سجل في الجدول.", "DocumentName": "اسم الوثيقة المرتبطة بالفاتورة.", "RecordID": "معرّف السجل المرتبط بالفاتورة.", "TheNumber": "رقم الفاتورة، يمكن أن يكون فارغًا.", "SupplierName": "اسم المورد، يمكن أن يكون فارغًا.", "InvoiceID": "معرّف الفاتورة.", "DetailsID": "معرّف التفاصيل المرتبط بالفاتورة.", "TheDate": "تاريخ الفاتورة، يمكن أن يكون فارغًا.", "CurrencyID": "معرّف العملة، يمكن أن يكون فارغًا.", "TheMethod": "طريقة الدفع، يمكن أن تكون فارغة.", "EnterTime": "وقت إدخال الفاتورة، يمكن أن يكون فارغًا.", "ItemID": "معرّف البند، يمكن أن يكون فارغًا.", "UnitID": "معرّف الوحدة، يمكن أن يكون فارغًا.", "UnitPrice": "سعر الوحدة، يمكن أن يكون فارغًا.", "Quantity": "الكمية، يمكن أن تكون فارغة.", "Bonus": "البونص، يمكن أن يكون فارغًا.", "TotalAmount": "المبلغ الإجمالي، يمكن أن يكون فارغًا.", "MainUnitQuantity": "كمية الوحدة الرئيسية، يمكن أن تكون فارغة.", "MainUnitPrice": "سعر الوحدة الرئيسية، يمكن أن يكون فارغًا.", "MainUnitID": "معرّف الوحدة الرئيسية، يمكن أن يكون فارغًا.", "StoreID": "معرّف المخزن، يمكن أن يكون فارغًا.", "BranchID": "معرّف الفرع، يمكن أن يكون فارغًا.", "ExchangeFactor": "عامل التحويل بين الوحدات.", "ClientID": "معرّف العميل، يمكن أن يكون فارغًا.", "MCAmount": "المبلغ الإجمالي بالعملة، يمكن أن يكون فارغًا.", "ExpiryDate": "تاريخ انتهاء الصلاحية، يمكن أن يكون فارغًا.", "MainUnitBonus": "بونص الوحدة الرئيسية، يمكن أن يكون فارغًا.", "ExchangePrice": "سعر الصرف، يمكن أن يكون فارغًا.", "DistributorID": "معرّف الموزع، يمكن أن يكون فارغًا.", "DistributorName": "اسم الموزع، يمكن أن يكون فارغًا.", "CostCenterID": "معرّف مركز التكلفة، يمكن أن يكون فارغًا.", "CostCenterName": "اسم مركز التكلفة، يمكن أن يكون فارغًا.", "TotalAmountByCurrencyInvetory": "المبلغ الإجمالي بالعملة في المخزون، يمكن أن يكون فارغًا.", "NewSubItemEntryID": "معرّف البند الفرعي الجديد."}, "analyticalValue": "الجدول يوفر قيمة تحليلية عالية من خلال تخزين تفاصيل الفواتير والبضائع، مما يساعد في تحليلات مالية وإدارية، مثل تحليل تكاليف الشراء، تقييم أداء الموردين، ومتابعة حركة المخزون.", "sqlExamples": [{"query": "SELECT SupplierName, SUM(TotalAmount) AS TotalSpent FROM tbltemp_Inv_MainInvoice GROUP BY SupplierName ORDER BY TotalSpent DESC;", "explanation": "هذا الاستعلام يعرض الموردين مع المبلغ الإجمالي الذي تم إنفاقه عليهم، مرتبًا تنازليًا حسب المبلغ الإجمالي. يساعد هذا الاستعلام في تقييم أداء الموردين ومعرفة الأكثر تكلفة."}, {"query": "SELECT StoreID, SUM(Quantity) AS TotalQuantity FROM tbltemp_Inv_MainInvoice WHERE TheDate BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY StoreID;", "explanation": "هذا الاستعلام يعرض مخازن مع الكمية الإجمالية للبضائع التي تم استلامها خلال عام 2023. يساعد هذا الاستعلام في متابعة حركة المخزون وتوزيع البضائع بين المخازن."}], "intelligentAnalysis": "الجدول يحتوي على بيانات مفصلة يمكن استخدامها في تحليلات متقدمة، مثل تحليل تكاليف الشراء، تقييم أداء الموردين، ومتابعة حركة المخزون. يمكن استخدام البيانات لبناء تقارير تفصيلية وتقديم رؤى قيمة للمديرين والمسؤولين.", "purpose": "الغرض الأساسي من الجدول هو تخزين تفاصيل الفواتير الرئيسية وتفاصيل البضائع المرتبطة بها، مما يساعد في إدارة المخزون وتقييم أداء الموردين.", "domain": "تجاري", "businessContext": "السياق التجاري للجدول هو إدارة المخزون وتقييم أداء الموردين في الشركات التجارية. يمكن استخدام البيانات في تحليلات مالية وإدارية لتحسين عمليات الشراء والمخزون.", "keyFields": ["ID", "InvoiceID", "RecordID", "TheDate", "TotalAmount"], "relatedTables": ["tblSuppliers", "tblClients", "tblStores", "tblBranches", "tblCostCenters"], "limitations": "القيود المحتملة تشمل وجود حقول فارغة قد تؤثر على دقة التحليلات، وعدم وجود مفاتيح خارجية قد يصعب ربط البيانات مع جداول أخرى.", "generatedAt": "2025-07-21T21:10:23.407Z"}, {"tableName": "tbltemp_ItemsMain", "description": "جدول tbltemp_ItemsMain يحتوي على بيانات خاصة بالعناصر والبضائع في نظام إدارة المخزون أو الفواتير. يتم استخدامه لتخزين المعلومات الأساسية حول العناصر، مثل معرف العنصر، اسمه، نوعه، الكمية، السعر، الخصومات، وغيرها من التفاصيل ذات الصلة.", "columnDescriptions": {"ID": "مفتاح رئيسي للجدول، يُستخدم لتعريف كل سجل بشكل فريد.", "ParentID": "معرف السجل الأب، يُستخدم في حالة وجود عناصر فرعية مرتبطة بعنصر رئيسي.", "RowVersion": "خانة زمنية تُستخدم لتتبع الإصدارات المختلفة للسجل.", "DocumentID": "معرف الوثيقة التي يرتبط بها العنصر، مثل الفاتورة أو الأمر.", "RecordNumber": "رقم السجل داخل الوثيقة.", "RecordID": "معرف السجل داخل الوثيقة.", "TheDate": "تاريخ إنشاء أو تحديث السجل.", "ClientID": "معرف العميل المرتبط بالعنصر، يمكن أن يكون فارغًا إذا لم يكن هناك عميل محدد.", "DistributorID": "معرف الموزع المرتبط بالعنصر، يمكن أن يكون فارغًا إذا لم يكن هناك موزع محدد.", "CurrencyID": "معرف العملة المستخدمة في المعاملة، يمكن أن يكون فارغًا إذا لم تكن هناك عملة محددة.", "TheMethodID": "معرف الطريقة المستخدمة في المعاملة، مثل الدفع النقدي أو الائتمان، يمكن أن يكون فارغًا.", "Discount": "مقدار الخصم المطبق على العنصر، يمكن أن يكون فارغًا إذا لم يكن هناك خصم.", "Notes": "ملاحظات إضافية حول العنصر، يمكن أن تكون فارغة.", "UserID": "معرف المستخدم الذي قام بإدخال أو تحديث السجل.", "BranchID": "معرف الفرع الذي ينتمي إليه العنصر.", "TheYear": "السنة التي تم فيها إنشاء السجل، يمكن أن تكون فارغة.", "DocumentName": "اسم الوثيقة المرتبطة بالعنصر، مثل اسم الفاتورة.", "TheNumber": "رقم العنصر داخل الوثيقة، يمكن أن يكون فارغًا.", "ClientName": "اسم العميل المرتبط بالعنصر، يمكن أن يكون فارغًا.", "DistributorName": "اسم الموزع المرتبط بالعنصر، يمكن أن يكون فارغًا.", "CurrencyName": "اسم العملة المستخدمة في المعاملة.", "TheMethod": "اسم الطريقة المستخدمة في المعاملة، مثل الدفع النقدي أو الائتمان، يمكن أن يكون فارغًا.", "UserName": "اسم المستخدم الذي قام بإدخال أو تحديث السجل.", "BranchName": "اسم الفرع الذي ينتمي إليه العنصر.", "CategoryID": "معرف الفئة التي ينتمي إليها العنصر، يمكن أن يكون فارغًا.", "FatherNumber": "رقم الفئة الأب للعنصر، يمكن أن يكون فارغًا.", "CategoryName": "اسم الفئة التي ينتمي إليها العنصر، يمكن أن يكون فارغًا.", "CategoryNumber": "رقم الفئة التي ينتمي إليها العنصر، يمكن أن يكون فارغًا.", "ItemID": "معرف العنصر، يمكن أن يكون فارغًا.", "UnitID": "معرف الوحدة التي يتم بيع العنصر بها، يمكن أن يكون فارغًا.", "ItemNumber": "رقم العنصر داخل الفئة.", "ItemName": "اسم العنصر، يمكن أن يكون فارغًا.", "ItemTypeID": "معرف نوع العنصر.", "ItemType": "نوع العنصر، مثل منتج أو خدمة، يمكن أن يكون فارغًا.", "ReorderPoint": "نقطة إعادة الطلب، وهي الكمية الدنيا التي يجب أن تصل إليها العناصر قبل إعادة طلب المزيد، يمكن أن تكون فارغة.", "ISActive": "حالة العنصر (نشط أو غير نشط)، يمكن أن تكون فارغة.", "ISExpiry": "هل العنصر له تاريخ انتهاء صلاحية؟ يمكن أن تكون فارغة.", "ExpiryPoint": "نقطة تاريخ الانتهاء، وهي عدد الأيام قبل تاريخ الانتهاء الذي يجب أن يتم فيه تنبيه المستخدم، يمكن أن تكون فارغة.", "UnitName": "اسم الوحدة التي يتم بيع العنصر بها، يمكن أن تكون فارغة.", "AccountFatherNumber": "رقم الحساب الأب، يمكن أن يكون فارغًا.", "AccountName": "اسم الحساب المرتبط بالعنصر، يمكن أن يكون فارغًا.", "AccountNumber": "رقم الحساب المرتبط بالعنصر، يمكن أن يكون فارغًا.", "CostCenterID": "معرف مركز التكلفة، يمكن أن يكون فارغًا.", "CostCenterName": "اسم مركز التكلفة، يمكن أن يكون فارغًا.", "CostCenterNumber": "رقم مركز التكلفة، يمكن أن يكون فارغًا.", "Barcode": "رمز الشريط الخاص بالعنصر، يمكن أن يكون فارغًا.", "UnitRank": "ترتيب الوحدة، يمكن أن يكون فارغًا.", "ExchangeFactor": "معامل التبادل بين الوحدات المختلفة للعنصر.", "PackageQuantity": "كمية العنصر في الحزمة، يمكن أن تكون فارغة.", "BarcodeID": "معرف رمز الشريط، يمكن أن يكون فارغًا.", "SerialNumber": "رقم التسلسلي للعنصر، يمكن أن يكون فارغًا.", "UnitPrice": "سعر الوحدة للعنصر، يمكن أن يكون فارغًا.", "ItemDiscount": "خصم العنصر، يمكن أن يكون فارغًا.", "McItemDiscountCurrencyMain": "خصم العنصر بالعملة الرئيسية، يمكن أن يكون فارغًا.", "McItemDiscount": "خصم العنصر في النظام، يمكن أن يكون فارغًا.", "Quantity": "الكمية المباعة أو المشتراة للعنصر، يمكن أن تكون فارغة.", "Bonus": "البونص المقدم مع العنصر، يمكن أن يكون فارغًا.", "ExpiryDate": "تاريخ انتهاء صلاحية العنصر، يمكن أن يكون فارغًا.", "Amount": "المبلغ الإجمالي للعنصر، يمكن أن يكون فارغًا.", "MCAmount": "المبلغ الإجمالي للعنصر في النظام، يمكن أن يكون فارغًا.", "MCAmountCurrencyMain": "المبلغ الإجمالي للعنصر بالعملة الرئيسية، يمكن أن يكون فارغًا.", "AccountID": "معرف الحسا<PERSON> المرتبط بالعنصر، يمكن أن يكون فارغًا.", "StoreID": "معرف المخزن الذي يحتوي على العنصر، يمكن أن يكون فارغًا.", "StoreName": "اسم المخزن الذي يحتوي على العنصر، يمكن أن يكون فارغًا.", "PackageUnitID": "معرف الوحدة الخاصة بالحزمة، يمكن أن يكون فارغًا.", "PackageUnitName": "اسم الوحدة الخاصة بالحزمة، يمكن أن يكون فارغًا.", "NextParentID": "معرف السجل الأب التالي، يمكن أن يكون فارغًا.", "ExchangePrice": "سعر التبادل للعنصر.", "ExchangePriceCurrencyInvetory": "سعر التبادل للعنصر بالعملة المخزونية."}, "analyticalValue": "الجدول يوفر قيمة تحليلية كبيرة في مجال إدارة المخزون والبيع، حيث يمكن استخدامه لتحليل حركة العناصر، متابعة الخصومات، مراقبة تواريخ الانتهاء، وتقييم أداء المبيعات والمشتريات. كما يمكن استخدامه لإعداد تقارير مفصلة حول العناصر والعملاء والموزعين.", "sqlExamples": [{"query": "SELECT ItemName, SUM(Quantity) AS TotalQuantity FROM tbltemp_ItemsMain GROUP BY ItemName ORDER BY TotalQuantity DESC;", "explanation": "هذا الاستعلام يعرض العناصر الأكثر مبيعًا بناءً على الكمية المباعة، مما يساعد في تحديد العناصر الأكثر شعبية."}, {"query": "SELECT ClientName, SUM(Amount) AS TotalAmount FROM tbltemp_ItemsMain WHERE TheDate BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY ClientName ORDER BY TotalAmount DESC;", "explanation": "هذا الاستعلام يعرض العملاء الذين قاموا بأكبر مبالغ مالية خلال سنة 2023، مما يساعد في تحديد العملاء الأكثر قيمة."}, {"query": "SELECT ItemName, AVG(UnitPrice) AS AveragePrice FROM tbltemp_ItemsMain GROUP BY ItemName;", "explanation": "هذا الاستعلام يحسب متوسط سعر الوحدة لكل عنصر، مما يساعد في تحليل أسعار البيع وتحديد أي تغييرات محتملة."}], "intelligentAnalysis": "الجدول يحتوي على العديد من الحقول التي يمكن استخدامها لإجراء تحليلات متعددة، مثل تحليل حركة المخزون، تحديد العناصر الأكثر مبيعًا، متابعة الخصومات، ومراقبة تواريخ الانتهاء. كما يمكن استخدام الحقول المتعلقة بالعملاء والموزعين لتحليل العلاقات التجارية وتحديد الفرص للتحسين.", "purpose": "الغرض الأساسي من الجدول هو تخزين وتنظيم البيانات المتعلقة بالعناصر والبضائع في نظام إدارة المخزون أو الفواتير، مما يسهل عملية تتبع حركة العناصر ومتابعة المعاملات المالية.", "domain": "تجاري", "businessContext": "السياق التجاري للجدول هو إدارة المخزون والبيع في الشركات التجارية، حيث يتم استخدامه لتسجيل وتحليل المعاملات المتعلقة بالعناصر والبضائع.", "keyFields": ["ID", "DocumentID", "ItemID", "TheDate", "UserID", "BranchID"], "relatedTables": [], "limitations": "الجدول لا يحتوي على مفاتيح خارجية، مما قد يجعل عملية الربط مع جداول أخرى أكثر تعقيدًا. كما أن بعض الحقول يمكن أن تكون فارغة، مما قد يؤثر على دقة التحليلات إذا لم يتم ملء هذه الحقول بشكل منتظم.", "generatedAt": "2025-07-21T21:12:20.529Z"}], "embeddings": {"tbltemp_Inv_MainInvoice": [], "tbltemp_ItemsMain": []}}