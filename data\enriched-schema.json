{"databaseName": "SalesTempDB", "databaseType": "mssql", "tables": [{"name": "tbltemp_Inv_MainInvoice", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SupplierName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "InvoiceID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DetailsID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "EnterTime", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "TotalAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MainUnitQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "MainUnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 20}, {"name": "MainUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "MCAmount", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "MainUnitBonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "ExchangePrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "TotalAmountByCurrencyInvetory", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "NewSubItemEntryID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_Inv_MainInvoice", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 908}, {"name": "tbltemp_ItemsMain", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RowVersion", "type": "timestamp", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "DocumentID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethodID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Discount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Notes", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255, "precision": null, "scale": null}, {"name": "UserID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheYear", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 10, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CategoryID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CategoryName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CategoryNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "ItemTypeID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemType", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "ReorderPoint", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 4}, {"name": "ISActive", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ISExpiry", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ExpiryPoint", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountFatherNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Account<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CostCenterNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Barcode", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitRank", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "PackageQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "BarcodeID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SerialNumber", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ItemD<PERSON>unt", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "McItemDiscountCurrencyMain", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "Mc<PERSON>temDiscount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "Amount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmountCurrencyMain", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "AccountID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "PackageUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "PackageUnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "NextParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangePrice", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExchangePriceCurrencyInvetory", "type": "decimal", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_ItemsMain", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 0}], "relationships": [], "extractedAt": "2025-07-21T20:53:29.278Z", "version": "1.0", "tableDescriptions": [{"tableName": "tbltemp_Inv_MainInvoice", "description": "جدول tbltemp_Inv_MainInvoice يُستخدم لتخزين بيانات الفواتير الرئيسية بشكل مؤقت، حيث يحتوي على معلومات مفصلة حول كل فاتورة مثل رقمها، تاريخها، القيمة الإجمالية، والمعلومات المتعلقة بالبائع والعميل. هذا الجدول يمكن استخدامه في عمليات الاستيراد والتصدير، وإدارة المخزون، وحساب التكاليف.", "columnDescriptions": {"ID": "رقم التسلسلي الفريد لكل سجل في الجدول.", "DocumentName": "اسم الوثيقة المرتبطة بالفاتورة.", "RecordID": "رقم السجل المرتبط بالفاتورة.", "TheNumber": "رقم الفاتورة، قد يكون فارغًا إذا لم يتم تحديده.", "SupplierName": "اسم المورد، قد يكون فارغًا إذا كانت الفاتورة ليست مرتبطة بمورد.", "InvoiceID": "رقم الفاتورة الفريد.", "DetailsID": "رقم تفاصيل الفاتورة.", "TheDate": "تاريخ إصدار الفاتورة، قد يكون فارغًا إذا لم يتم تحديده.", "CurrencyID": "رقم العملة المستخدمة في الفاتورة، قد يكون فارغًا إذا كانت الفاتورة بالعملة المحلية.", "TheMethod": "طريقة الدفع، قد تكون فارغة إذا لم يتم تحديد طريقة الدفع.", "EnterTime": "وقت إدخال الفاتورة في النظام، قد يكون فارغًا إذا لم يتم تحديده.", "ItemID": "رقم البند أو السلعة في الفاتورة، قد يكون فارغًا إذا لم يتم تحديده.", "UnitID": "رقم الوحدة التي يتم بيع السلعة بها، قد يكون فارغًا إذا لم يتم تحديده.", "UnitPrice": "سعر الوحدة للسلعة، قد يكون فارغًا إذا لم يتم تحديده.", "Quantity": "كمية السلعة المباعة، قد تكون فارغة إذا لم يتم تحديدها.", "Bonus": "البونص أو الخصم المقدم مع السلعة، قد يكون فارغًا إذا لم يتم تحديده.", "TotalAmount": "القيمة الإجمالية للفاتورة، قد تكون فارغة إذا لم يتم حسابها.", "MainUnitQuantity": "كمية الوحدة الرئيسية للسلعة، قد تكون فارغة إذا لم يتم تحديدها.", "MainUnitPrice": "سعر الوحدة الرئيسية للسلعة، قد يكون فارغًا إذا لم يتم تحديده.", "MainUnitID": "رقم الوحدة الرئيسية للسلعة، قد يكون فارغًا إذا لم يتم تحديده.", "StoreID": "رقم المخزن الذي تم تخزين السلعة فيه، قد يكون فارغًا إذا لم يتم تحديده.", "BranchID": "رقم الفرع الذي تم إصدار الفاتورة منه، قد يكون فارغًا إذا لم يتم تحديده.", "ExchangeFactor": "معامل الصرف بين الوحدات المختلفة للسلعة.", "ClientID": "رقم العميل، قد يكون فارغًا إذا كانت الفاتورة ليست مرتبطة بعميل.", "MCAmount": "القيمة الإجمالية بالعملة الرئيسية، قد تكون فارغة إذا لم يتم حسابها.", "ExpiryDate": "تاريخ انتهاء الصلاحية للسلعة، قد يكون فارغًا إذا لم يتم تحديده.", "MainUnitBonus": "البونص أو الخصم المقدم مع الوحدة الرئيسية للسلعة، قد يكون فارغًا إذا لم يتم تحديده.", "ExchangePrice": "سعر الصرف للعملة المستخدمة في الفاتورة، قد يكون فارغًا إذا كانت الفاتورة بالعملة المحلية.", "DistributorID": "رقم الموزع، قد يكون فارغًا إذا كانت الفاتورة ليست مرتبطة بموزع.", "DistributorName": "اسم الموزع، قد يكون فارغًا إذا كانت الفاتورة ليست مرتبطة بموزع.", "CostCenterID": "رقم مركز التكلفة، قد يكون فارغًا إذا لم يتم تحديده.", "CostCenterName": "اسم مركز التكلفة، قد يكون فارغًا إذا لم يتم تحديده.", "TotalAmountByCurrencyInvetory": "القيمة الإجمالية للفاتورة بالعملة المخزونية، قد تكون فارغة إذا لم يتم حسابها.", "NewSubItemEntryID": "رقم السجل الجديد للبند الفرعي، يستخدم عند إضافة بنود فرعية جديدة."}, "analyticalValue": "الجدول يوفر قيمة تحليلية كبيرة في مجال إدارة الفواتير والمخزون، حيث يمكن استخدامه لتحليل الأنماط في شراء السلع، تقييم أداء الموردين والموزعين، ومراقبة التكاليف والدخل. كما يمكن استخدامه لإنشاء تقارير مالية وإدارية دقيقة.", "sqlExamples": [{"query": "SELECT SupplierName, SUM(TotalAmount) AS TotalSpent FROM tbltemp_Inv_MainInvoice WHERE TheDate BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY SupplierName ORDER BY TotalSpent DESC;", "explanation": "هذا الاستعلام يجمع إجمالي المبالغ المصروفة لكل مورد خلال سنة 2023، مما يساعد في تقييم أداء الموردين ومعرفة أكبر الموردين من حيث الإنفاق."}, {"query": "SELECT ItemID, SUM(Quantity) AS TotalQuantity FROM tbltemp_Inv_MainInvoice WHERE TheDate BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY ItemID ORDER BY TotalQuantity DESC;", "explanation": "هذا الاستعلام يجمع إجمالي الكمية المباعة لكل سلعة خلال سنة 2023، مما يساعد في تحديد أكثر السلع مبيعًا وتحديد الطلب عليها."}, {"query": "SELECT TheDate, COUNT(*) AS InvoiceCount FROM tbltemp_Inv_MainInvoice WHERE TheDate IS NOT NULL GROUP BY TheDate ORDER BY TheDate ASC;", "explanation": "هذا الاستعلام يحسب عدد الفواتير الصادرة في كل يوم، مما يساعد في تحليل الأنماط الزمنية لإصدار الفواتير."}], "intelligentAnalysis": "الجدول يحتوي على معلومات مفصلة عن الفواتير، مما يسمح بإجراء تحليلات متعددة مثل تحليل الإنفاق حسب المورد، تحليل المبيعات حسب السلعة، وتتبع حركة المخزون. يمكن استخدام الحقول الفارغة لتخزين بيانات إضافية عند الحاجة، مما يزيد من مرونة الجدول.", "purpose": "الغرض الأساسي من الجدول هو تخزين بيانات الفواتير الرئيسية بشكل مؤقت قبل إدراجها في الجداول الرئيسية، مما يساعد في التحقق من البيانات وإجراء العمليات اللازمة عليها.", "domain": "تجاري", "businessContext": "يُستخدم الجدول في سياق الأعمال التجارية لإدارة الفواتير والمخزون، حيث يمكن أن يكون جزءًا من نظام إدارة المبيعات والمشتريات في شركة تجارية.", "keyFields": ["ID", "InvoiceID", "RecordID"], "relatedTables": [], "limitations": "الجدول لا يحتوي على مفاتيح خارجية، مما قد يحد من القدرة على ربط البيانات بجداول أخرى بشكل مباشر. كما أن بعض الحقول يمكن أن تكون فارغة، مما قد يؤثر على دقة التحليلات إذا لم يتم ملؤها بشكل صحيح.", "generatedAt": "2025-07-21T20:54:29.992Z"}, {"tableName": "tbltemp_ItemsMain", "description": "جدول tbltemp_ItemsMain يحتوي على بيانات تفصيلية عن العناصر والبضائع في نظام إدارة المخزون والمستودعات. يوفر هذا الجدول معلومات متكاملة حول العناصر، بما في ذلك تفاصيل العملاء والموزعين والعملات والطرق المستخدمة، بالإضافة إلى تفاصيل العناصر مثل الأصناف والوحدات والأسعار والكميات.", "columnDescriptions": {"ID": "مفتاح رئيسي فريد لكل سجل في الجدول.", "ParentID": "مفتا<PERSON> خارجي يشير إلى السجل الأبوة، يمكن أن يكون فارغًا إذا كان السجل ليس له سجل أب.", "RowVersion": "ختم زمني يُستخدم لتعقب التغييرات في السجل، يمكن أن يكون فارغًا.", "DocumentID": "مفتا<PERSON> خارجي يشير إلى الوثيقة المرتبطة بالعنصر.", "RecordNumber": "رقم السجل الفريد داخل الوثيقة.", "RecordID": "مفتا<PERSON> خارجي يشير إلى السجل المرتبط بالعنصر.", "TheDate": "تاريخ إنشاء أو تحديث السجل.", "ClientID": "مفتا<PERSON> خارجي يشير إلى العميل المرتبط بالعنصر، يمكن أن يكون فارغًا.", "DistributorID": "مفتا<PERSON> خارجي يشير إلى الموزع المرتبط بالعنصر، يمكن أن يكون فارغًا.", "CurrencyID": "مفتاح خارجي يشير إلى العملة المستخدمة في المعاملة، يمكن أن يكون فارغًا.", "TheMethodID": "مفتاح خارجي يشير إلى الطريقة المستخدمة في المعاملة، يمكن أن يكون فارغًا.", "Discount": "نسبة الخصم المطبقة على العنصر، يمكن أن تكون فارغة.", "Notes": "ملاحظات تفصيلية حول السجل، يمكن أن تكون فارغة.", "UserID": "مفتا<PERSON> خارجي يشير إلى المستخدم الذي قام بإنشاء أو تحديث السجل.", "BranchID": "مفتا<PERSON> خارجي يشير إلى الفرع المرتبط بالعنصر.", "TheYear": "السنة التي تم فيها إنشاء السجل، يمكن أن تكون فارغة.", "DocumentName": "اسم الوثيقة المرتبطة بالعنصر.", "TheNumber": "رقم الوثيقة المرتبطة بالعنصر، يمكن أن يكون فارغًا.", "ClientName": "اسم العميل المرتبط بالعنصر، يمكن أن يكون فارغًا.", "DistributorName": "اسم الموزع المرتبط بالعنصر، يمكن أن يكون فارغًا.", "CurrencyName": "اسم العملة المستخدمة في المعاملة.", "TheMethod": "اسم الطريقة المستخدمة في المعاملة، يمكن أن تكون فارغة.", "UserName": "اسم المستخدم الذي قام بإنشاء أو تحديث السجل.", "BranchName": "اسم الفرع المرتبط بالعنصر.", "CategoryID": "مفتا<PERSON> خارجي يشير إلى الفئة المرتبطة بالعنصر، يمكن أن يكون فارغًا.", "FatherNumber": "رقم الفئة الأب للعنصر، يمكن أن يكون فارغًا.", "CategoryName": "اسم الفئة المرتبطة بالعنصر، يمكن أن يكون فارغًا.", "CategoryNumber": "رقم الفئة المرتبطة بالعنصر، يمكن أن يكون فارغًا.", "ItemID": "مفتا<PERSON> خارجي يشير إلى العنصر، يمكن أن يكون فارغًا.", "UnitID": "مفتا<PERSON> خارجي يشير إلى الوحدة المرتبطة بالعنصر، يمكن أن يكون فارغًا.", "ItemNumber": "رقم العنصر الفريد.", "ItemName": "اسم العنصر، يمكن أن يكون فارغًا.", "ItemTypeID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى نوع العنصر.", "ItemType": "نوع العنصر، يمكن أن يكون فارغًا.", "ReorderPoint": "نقطة إعادة الطلب للعنصر، يمكن أن تكون فارغة.", "ISActive": "حالة العنصر (نشط/غير نشط)، يمكن أن تكون فارغة.", "ISExpiry": "حالة انتهاء صلاحية العنصر (منتهي الصلاحية/غير منتهي الصلاحية)، يمكن أن تكون فارغة.", "ExpiryPoint": "نقطة انتهاء الصلاحية للعنصر، يمكن أن تكون فارغة.", "UnitName": "اسم الوحدة المرتبطة بالعنصر، يمكن أن يكون فارغًا.", "AccountFatherNumber": "رقم الحساب الأب للعنصر، يمكن أن يكون فارغًا.", "AccountName": "اسم الحساب المرتبط بالعنصر، يمكن أن يكون فارغًا.", "AccountNumber": "رقم الحساب المرتبط بالعنصر، يمكن أن يكون فارغًا.", "CostCenterID": "مفتا<PERSON> خارجي يشير إلى مركز التكلفة المرتبط بالعنصر، يمكن أن يكون فارغًا.", "CostCenterName": "اسم مركز التكلفة المرتبط بالعنصر، يمكن أن يكون فارغًا.", "CostCenterNumber": "رقم مركز التكلفة المرتبط بالعنصر، يمكن أن يكون فارغًا.", "Barcode": "رمز الباركود للعنصر، يمكن أن يكون فارغًا.", "UnitRank": "ترتيب الوحدة، يمكن أن يكون فارغًا.", "ExchangeFactor": "معامل التبادل للعنصر.", "PackageQuantity": "كمية الحزمة، يمكن أن تكون فارغة.", "BarcodeID": "مفتا<PERSON> خارجي يشير إلى رمز الباركود، يمكن أن يكون فارغًا.", "SerialNumber": "رقم التسلسلي للعنصر، يمكن أن يكون فارغًا.", "UnitPrice": "سعر الوحدة للعنصر، يمكن أن يكون فارغًا.", "ItemDiscount": "خصم العنصر، يمكن أن يكون فارغًا.", "McItemDiscountCurrencyMain": "خصم العنصر بالعملة الرئيسية، يمكن أن يكون فارغًا.", "McItemDiscount": "خصم العنصر، يمكن أن يكون فارغًا.", "Quantity": "كمية العنصر، يمكن أن تكون فارغة.", "Bonus": "بونص العنصر، يمكن أن يكون فارغًا.", "ExpiryDate": "تاريخ انتهاء صلاحية العنصر، يمكن أن يكون فارغًا.", "Amount": "المبلغ الإجمالي للعنصر، يمكن أن يكون فارغًا.", "MCAmount": "المبلغ الإجمالي بالعملة الرئيسية، يمكن أن يكون فارغًا.", "MCAmountCurrencyMain": "المبلغ الإجمالي بالعملة الرئيسية، يمكن أن يكون فارغًا.", "AccountID": "مفتا<PERSON> خارجي يشير إلى الحساب المرتبط بالعنصر، يمكن أن يكون فارغًا.", "StoreID": "مفتا<PERSON> خارجي يشير إلى المستودع المرتبط بالعنصر، يمكن أن يكون فارغًا.", "StoreName": "اسم المستودع المرتبط بالعنصر، يمكن أن يكون فارغًا.", "PackageUnitID": "مفتا<PERSON> خارجي يشير إلى وحدة الحزمة المرتبط بالعنصر، يمكن أن يكون فارغًا.", "PackageUnitName": "اسم وحدة الحزمة المرتبط بالعنصر، يمكن أن يكون فارغًا.", "NextParentID": "مفتا<PERSON> خارجي يشير إلى السجل الأبوة التالي، يمكن أن يكون فارغًا.", "ExchangePrice": "سعر التبادل للعنصر.", "ExchangePriceCurrencyInvetory": "سعر التبادل للعنصر بالعملة المخزونية."}, "analyticalValue": "الجدول يوفر قيمة تحليلية وإحصائية كبيرة في مجال إدارة المخزون والمستودعات، حيث يوفر تفاصيل دقيقة عن العناصر والبضائع، بما في ذلك تفاصيل العملاء والموزعين والعملات والطرق المستخدمة. يمكن استخدام البيانات في هذا الجدول لتحليل الاتجاهات في المبيعات والمشتريات، تقييم أداء العناصر، وتحسين إدارة المخزون.", "sqlExamples": [{"query": "SELECT ItemName, SUM(Quantity) AS TotalQuantity FROM tbltemp_ItemsMain GROUP BY ItemName ORDER BY TotalQuantity DESC;", "explanation": "هذا الاستعلام يعرض العناصر مع مجموع الكميات لكل عنصر، مرتبة تنازليًا حسب الكمية الإجمالية. يمكن استخدام هذا الاستعلام لتقييم العناصر الأكثر مبيعًا."}, {"query": "SELECT ClientName, SUM(Amount) AS TotalAmount FROM tbltemp_ItemsMain WHERE TheDate BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY ClientName ORDER BY TotalAmount DESC;", "explanation": "هذا الاستعلام يعرض العملاء مع المبلغ الإجمالي للمعاملات لكل عميل خلال عام 2023، مرتبة تنازليًا حسب المبلغ الإجمالي. يمكن استخدام هذا الاستعلام لتقييم العملاء الأكثر إيرادًا."}], "intelligentAnalysis": "الجدول يحتوي على بيانات مفصلة ومتكاملة تسمح بإجراء تحليلات متعددة الأبعاد. يمكن استخدام البيانات لتحليل الاتجاهات في المبيعات والمشتريات، تقييم أداء العناصر، وتحسين إدارة المخزون. كما يمكن استخدام البيانات لتحديد العناصر التي تحتاج إلى إعادة طلب، والعناصر التي تقترب من انتهاء الصلاحية. يمكن أيضًا استخدام البيانات لتحليل أداء العملاء والموزعين وتحسين العلاقات معهم.", "purpose": "الغرض الأساسي من الجدول هو توفير تفاصيل دقيقة ومتكاملة عن العناصر والبضائع في نظام إدارة المخزون والمستودعات، بما في ذلك تفاصيل العملاء والموزعين والعملات والطرق المستخدمة.", "domain": "تجاري", "businessContext": "السياق التجاري للجدول هو إدارة المخزون والمستودعات في الشركات التجارية والصناعية. يمكن استخدام البيانات في هذا الجدول لتحسين إدارة المخزون، تقييم أداء العناصر، وتحسين العلاقات مع العملاء والموزعين.", "keyFields": ["ID", "DocumentID", "RecordNumber", "ItemID", "ItemNumber", "Quantity", "Amount"], "relatedTables": ["tbltemp_Clients", "tbltemp_Distributors", "tbltemp_Currencies", "tbltemp_Methods", "tbltemp_Users", "tbltemp_Branches", "tbltemp_Categories", "tbltemp_Items", "tbltemp_Units", "tbltemp_Accounts", "tbltemp_CostCenters", "tbltemp_Stores"], "limitations": "الجدول يحتوي على العديد من الحقول التي يمكن أن تكون فارغة، مما قد يؤدي إلى نقص في البيانات في بعض السجلات. كما أن عدم وجود مفاتيح خارجية قد يحد من قدرة النظام على الربط بين الجداول بشكل فعال.", "generatedAt": "2025-07-21T20:55:38.626Z"}], "embeddings": {"tbltemp_Inv_MainInvoice": [], "tbltemp_ItemsMain": []}}