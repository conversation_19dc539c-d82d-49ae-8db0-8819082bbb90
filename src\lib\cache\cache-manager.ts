import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';

// أنواع البيانات المخزنة مؤقتاً
export interface CacheEntry<T = any> {
  key: string;
  data: T;
  timestamp: number;
  expiresAt: number;
  metadata?: Record<string, any>;
}

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum number of entries
  persistToDisk?: boolean; // Save to disk
}

export class CacheManager {
  private cache = new Map<string, CacheEntry>();
  private cacheDir: string;
  private options: Required<CacheOptions>;

  constructor(options: CacheOptions = {}) {
    this.options = {
      ttl: options.ttl || 24 * 60 * 60 * 1000, // 24 hours default
      maxSize: options.maxSize || 1000,
      persistToDisk: options.persistToDisk ?? true
    };
    
    this.cacheDir = path.join(process.cwd(), 'data', 'cache');
    this.initializeCache();
  }

  private async initializeCache(): Promise<void> {
    if (this.options.persistToDisk) {
      try {
        await fs.mkdir(this.cacheDir, { recursive: true });
        await this.loadFromDisk();
      } catch (error) {
        console.error('خطأ في تهيئة الكاش:', error);
      }
    }
  }

  // إنشاء مفتاح فريد للبيانات
  private generateKey(input: string): string {
    return crypto.createHash('md5').update(input).digest('hex');
  }

  // فحص ما إذا كان المفتاح منتهي الصلاحية
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() > entry.expiresAt;
  }

  // تنظيف البيانات المنتهية الصلاحية
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));

    // إذا تجاوز الحد الأقصى، احذف الأقدم
    if (this.cache.size > this.options.maxSize) {
      const entries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp);
      
      const toDelete = entries.slice(0, this.cache.size - this.options.maxSize);
      toDelete.forEach(([key]) => this.cache.delete(key));
    }
  }

  // حفظ البيانات في الكاش
  async set<T>(key: string, data: T, options?: { ttl?: number; metadata?: Record<string, any> }): Promise<void> {
    const cacheKey = this.generateKey(key);
    const now = Date.now();
    const ttl = options?.ttl || this.options.ttl;

    const entry: CacheEntry<T> = {
      key: cacheKey,
      data,
      timestamp: now,
      expiresAt: now + ttl,
      metadata: options?.metadata
    };

    this.cache.set(cacheKey, entry);
    this.cleanup();

    if (this.options.persistToDisk) {
      await this.saveToDisk(cacheKey, entry);
    }
  }

  // استرجاع البيانات من الكاش
  async get<T>(key: string): Promise<T | null> {
    const cacheKey = this.generateKey(key);
    const entry = this.cache.get(cacheKey);

    if (!entry) {
      // محاولة التحميل من القرص
      if (this.options.persistToDisk) {
        const diskEntry = await this.loadFromDisk(cacheKey);
        if (diskEntry && !this.isExpired(diskEntry)) {
          this.cache.set(cacheKey, diskEntry);
          return diskEntry.data as T;
        }
      }
      return null;
    }

    if (this.isExpired(entry)) {
      this.cache.delete(cacheKey);
      if (this.options.persistToDisk) {
        await this.deleteFromDisk(cacheKey);
      }
      return null;
    }

    return entry.data as T;
  }

  // فحص وجود البيانات في الكاش
  async has(key: string): Promise<boolean> {
    const data = await this.get(key);
    return data !== null;
  }

  // حذف البيانات من الكاش
  async delete(key: string): Promise<boolean> {
    const cacheKey = this.generateKey(key);
    const deleted = this.cache.delete(cacheKey);

    if (this.options.persistToDisk) {
      await this.deleteFromDisk(cacheKey);
    }

    return deleted;
  }

  // مسح جميع البيانات
  async clear(): Promise<void> {
    this.cache.clear();

    if (this.options.persistToDisk) {
      try {
        const files = await fs.readdir(this.cacheDir);
        await Promise.all(
          files.map(file => fs.unlink(path.join(this.cacheDir, file)))
        );
      } catch (error) {
        console.error('خطأ في مسح ملفات الكاش:', error);
      }
    }
  }

  // الحصول على إحصائيات الكاش
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    memoryUsage: number;
  } {
    const size = this.cache.size;
    const memoryUsage = JSON.stringify(Array.from(this.cache.values())).length;

    return {
      size,
      maxSize: this.options.maxSize,
      hitRate: 0, // يمكن تحسينه لاحقاً
      memoryUsage
    };
  }

  // حفظ البيانات في القرص
  private async saveToDisk(key: string, entry: CacheEntry): Promise<void> {
    try {
      const filePath = path.join(this.cacheDir, `${key}.json`);
      await fs.writeFile(filePath, JSON.stringify(entry), 'utf-8');
    } catch (error) {
      console.error('خطأ في حفظ الكاش في القرص:', error);
    }
  }

  // تحميل البيانات من القرص
  private async loadFromDisk(key?: string): Promise<CacheEntry | null> {
    try {
      if (key) {
        const filePath = path.join(this.cacheDir, `${key}.json`);
        const data = await fs.readFile(filePath, 'utf-8');
        return JSON.parse(data) as CacheEntry;
      } else {
        // تحميل جميع الملفات
        const files = await fs.readdir(this.cacheDir);
        for (const file of files) {
          if (file.endsWith('.json')) {
            const filePath = path.join(this.cacheDir, file);
            const data = await fs.readFile(filePath, 'utf-8');
            const entry = JSON.parse(data) as CacheEntry;
            
            if (!this.isExpired(entry)) {
              this.cache.set(entry.key, entry);
            } else {
              await fs.unlink(filePath);
            }
          }
        }
      }
    } catch (error) {
      // الملف غير موجود أو خطأ في القراءة
      return null;
    }
    return null;
  }

  // حذف البيانات من القرص
  private async deleteFromDisk(key: string): Promise<void> {
    try {
      const filePath = path.join(this.cacheDir, `${key}.json`);
      await fs.unlink(filePath);
    } catch (error) {
      // الملف غير موجود
    }
  }
}

// إنشاء مثيل مشترك للكاش
export const cacheManager = new CacheManager({
  ttl: 24 * 60 * 60 * 1000, // 24 ساعة
  maxSize: 1000,
  persistToDisk: true
});

// كاش متخصص للاستعلامات
export const queryCache = new CacheManager({
  ttl: 60 * 60 * 1000, // ساعة واحدة
  maxSize: 500,
  persistToDisk: true
});

// كاش متخصص للأوصاف
export const descriptionCache = new CacheManager({
  ttl: 7 * 24 * 60 * 60 * 1000, // أسبوع
  maxSize: 200,
  persistToDisk: true
});
