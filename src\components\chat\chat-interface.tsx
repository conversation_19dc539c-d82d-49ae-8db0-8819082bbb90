"use client"

import React, { useState, useRef, useEffect } from 'react';
import { ChatMessageComponent, ChatMessage } from './chat-message';
import { ChatInput } from './chat-input';
import { MessageSquare, Trash2, Download } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatInterfaceProps {
  onSendMessage: (message: string) => Promise<ChatMessage>;
  isAgentReady?: boolean;
  className?: string;
}

export function ChatInterface({ 
  onSendMessage, 
  isAgentReady = false,
  className = ""
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Add welcome message when agent is ready
  useEffect(() => {
    if (isAgentReady && messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        type: 'assistant',
        content: 'مرحباً! أنا الوكيل الذكي لقواعد البيانات. يمكنني مساعدتك في:\n\n• تحليل بيانات قاعدة البيانات\n• إنشاء استعلامات SQL\n• الإجابة على أسئلتك حول البيانات\n• تقديم إحصائيات ومعلومات مفيدة\n\nما الذي تود معرفته؟',
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [isAgentReady, messages.length]);

  const handleSendMessage = async (messageContent: string) => {
    if (!isAgentReady) {
      return;
    }

    // Add user message
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: messageContent,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Get response from agent
      const response = await onSendMessage(messageContent);
      setMessages(prev => [...prev, response]);
    } catch (error) {
      // Add error message
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        type: 'system',
        content: 'عذراً، حدث خطأ أثناء معالجة سؤالك. يرجى المحاولة مرة أخرى.',
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyQuery = async (query: string) => {
    try {
      await navigator.clipboard.writeText(query);
    } catch (error) {
      console.error('فشل في نسخ الاستعلام:', error);
    }
  };

  const clearChat = () => {
    setMessages([]);
  };

  const exportChat = () => {
    const chatData = {
      timestamp: new Date().toISOString(),
      messages: messages.map(msg => ({
        type: msg.type,
        content: msg.content,
        timestamp: msg.timestamp.toISOString(),
        sqlQuery: msg.sqlQuery,
        explanation: msg.explanation,
        confidence: msg.confidence,
        relevantTables: msg.relevantTables,
        executionTime: msg.executionTime,
        error: msg.error
      }))
    };

    const blob = new Blob([JSON.stringify(chatData, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!isAgentReady) {
    return (
      <div className={cn("flex flex-col h-full bg-gray-50", className)}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              الوكيل الذكي غير جاهز
            </h3>
            <p className="text-gray-600">
              يرجى إعداد قاعدة البيانات أولاً لبدء المحادثة
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-gray-50", className)}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <MessageSquare className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                الوكيل الذكي
              </h2>
              <p className="text-sm text-gray-600">
                {messages.length > 1 ? `${messages.length - 1} رسالة` : 'جاهز للمحادثة'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {messages.length > 1 && (
              <>
                <button
                  onClick={exportChat}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                  title="تصدير المحادثة"
                >
                  <Download className="w-4 h-4" />
                </button>
                <button
                  onClick={clearChat}
                  className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  title="مسح المحادثة"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Messages */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {messages.map((message) => (
          <ChatMessageComponent
            key={message.id}
            message={message}
            onCopyQuery={handleCopyQuery}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <ChatInput
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
        disabled={!isAgentReady}
        placeholder={
          isAgentReady 
            ? "اسأل عن بياناتك..." 
            : "يرجى إعداد قاعدة البيانات أولاً"
        }
      />
    </div>
  );
}
