"use client"

import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Download, Search, Filter } from 'lucide-react';
import { cn } from '@/lib/utils';
import { translateColumn, translateDataArray, formatColumnValue } from '@/lib/utils/column-translator';

interface DataTableProps {
  data: any[];
  columns?: string[];
  title?: string;
  className?: string;
  maxRows?: number;
  searchable?: boolean;
  exportable?: boolean;
}

export function DataTable({ 
  data, 
  columns, 
  title,
  className = "",
  maxRows = 10,
  searchable = true,
  exportable = true
}: DataTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // استخراج الأعمدة من البيانات إذا لم يتم تمريرها
  const originalColumns = columns || (data.length > 0 ? Object.keys(data[0]) : []);
  const tableColumns = originalColumns.map(col => translateColumn(col));

  // تصفية البيانات حسب البحث
  const filteredData = data.filter(row =>
    searchTerm === '' ||
    originalColumns.some(col =>
      String(row[col]).toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  // ترتيب البيانات
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortColumn) return 0;

    // العثور على العمود الأصلي من العمود المترجم
    const originalColumnIndex = tableColumns.indexOf(sortColumn);
    const originalColumnName = originalColumns[originalColumnIndex];

    const aVal = a[originalColumnName];
    const bVal = b[originalColumnName];

    if (aVal === bVal) return 0;

    const comparison = aVal < bVal ? -1 : 1;
    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // تقسيم البيانات إلى صفحات
  const totalPages = Math.ceil(sortedData.length / maxRows);
  const startIndex = (currentPage - 1) * maxRows;
  const paginatedData = sortedData.slice(startIndex, startIndex + maxRows);

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const exportToCSV = () => {
    const csvContent = [
      tableColumns.join(','),
      ...sortedData.map(row => 
        tableColumns.map(col => `"${String(row[col]).replace(/"/g, '""')}"`).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `data-export-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatCellValue = (value: any, columnName: string) => {
    return formatColumnValue(columnName, value);
  };

  if (data.length === 0) {
    return (
      <div className={cn("bg-white rounded-lg shadow-md p-8 text-center", className)}>
        <div className="text-gray-500">
          <Filter className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">لا توجد بيانات</h3>
          <p>لم يتم العثور على أي نتائج لعرضها</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("bg-white rounded-lg shadow-md overflow-hidden", className)}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            {title && (
              <h3 className="text-lg font-semibold text-gray-900 mb-1">{title}</h3>
            )}
            <p className="text-sm text-gray-600">
              {filteredData.length} من {data.length} سجل
            </p>
          </div>

          <div className="flex items-center gap-2">
            {searchable && (
              <div className="relative">
                <Search className="w-4 h-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="بحث..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-3 pr-10 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            )}

            {exportable && (
              <button
                onClick={exportToCSV}
                className="flex items-center gap-1 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                <Download className="w-4 h-4" />
                تصدير
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              {tableColumns.map((column) => (
                <th
                  key={column}
                  onClick={() => handleSort(column)}
                  className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span>{column}</span>
                    {sortColumn === column && (
                      <span className="text-blue-600">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedData.map((row, index) => (
              <tr key={index} className="hover:bg-gray-50 transition-colors">
                {tableColumns.map((column, colIndex) => {
                  const originalColumnName = originalColumns[colIndex];
                  return (
                    <td key={column} className="px-4 py-3 text-sm text-gray-900">
                      {formatCellValue(row[originalColumnName], originalColumnName)}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 py-3 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            عرض {startIndex + 1} إلى {Math.min(startIndex + maxRows, filteredData.length)} من {filteredData.length} نتيجة
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="w-4 h-4" />
            </button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={cn(
                      "px-3 py-1 text-sm rounded-md transition-colors",
                      currentPage === page
                        ? "bg-blue-600 text-white"
                        : "text-gray-600 hover:bg-gray-100"
                    )}
                  >
                    {page}
                  </button>
                );
              })}
            </div>

            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
