"use client"

import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';

interface SimpleProgressProps {
  title: string;
  description?: string;
  progress: number;
  isComplete?: boolean;
  hasError?: boolean;
  errorMessage?: string;
  className?: string;
}

export function SimpleProgress({ 
  title, 
  description, 
  progress, 
  isComplete = false, 
  hasError = false, 
  errorMessage,
  className = ""
}: SimpleProgressProps) {
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          {description && (
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          )}
        </div>
        
        <div className="flex items-center">
          {hasError ? (
            <AlertCircle className="w-6 h-6 text-red-500" />
          ) : isComplete ? (
            <CheckCircle className="w-6 h-6 text-green-500" />
          ) : (
            <Loader2 className="w-6 h-6 text-blue-500 animate-spin" />
          )}
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">التقدم</span>
          <span className="text-sm font-medium text-gray-700">{Math.round(progress)}%</span>
        </div>
        
        <Progress 
          value={progress} 
          className={`h-2 ${hasError ? 'bg-red-100' : isComplete ? 'bg-green-100' : 'bg-gray-200'}`}
        />

        {hasError && errorMessage && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-700">{errorMessage}</p>
          </div>
        )}

        {isComplete && !hasError && (
          <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-700">تم الانتهاء بنجاح!</p>
          </div>
        )}
      </div>
    </div>
  );
}
