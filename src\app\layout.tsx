import type { Metadata } from "next";
import "./globals.css";

// استخدام خطوط النظام بدلاً من Google Fonts لتجنب مشاكل الاتصال
const fontClass = "font-sans";

export const metadata: Metadata = {
  title: "AI Database Agent - نظام الوكيل الذكي لقواعد البيانات",
  description: "نظام ذكاء صناعي متكامل لفهم وتحليل قواعد البيانات وتوليد استعلامات دقيقة",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${fontClass} antialiased bg-gray-50`}>
        {children}
      </body>
    </html>
  );
}
