import { NextRequest, NextResponse } from 'next/server';
import { DatabaseAgent } from '@/lib/agent/database-agent';
import { SchemaManager } from '@/lib/database/schema-manager';
import { AIProviderManager } from '@/lib/ai/ai-provider-manager';
import { loadSelectedProvider } from '@/lib/ai/ai-provider-storage';

// إنشاء مثيل مشترك للوكيل
let agentInstance: DatabaseAgent | null = null;

export async function POST(request: NextRequest) {
  try {
    const { question } = await request.json();

    if (!question || typeof question !== 'string') {
      return NextResponse.json(
        { success: false, error: 'السؤال مطلوب' },
        { status: 400 }
      );
    }

    // إنشاء الوكيل إذا لم يكن موجوداً
    if (!agentInstance) {
      // تحميل النموذج المحفوظ أولاً
      const savedProvider = await loadSelectedProvider();
      if (savedProvider) {
        const aiManager = AIProviderManager.getInstance();
        aiManager.setProvider(savedProvider);
        console.log(`🤖 تم تحميل النموذج المحفوظ للاستعلام: ${savedProvider}`);
      }

      agentInstance = new DatabaseAgent();

      // محاولة تحميل معلومات الاتصال المحفوظة
      const schemaManager = SchemaManager.getInstance();
      const savedConnection = await schemaManager.loadConnection();

      await agentInstance.initialize(savedConnection || undefined);
    }

    // معالجة السؤال
    const result = await agentInstance.processQuery(question);

    return NextResponse.json({ 
      success: true, 
      result 
    });

  } catch (error) {
    console.error('خطأ في معالجة الاستعلام:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'خطأ غير معروف' 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // إنشاء الوكيل إذا لم يكن موجوداً
    if (!agentInstance) {
      agentInstance = new DatabaseAgent();

      // محاولة تحميل معلومات الاتصال المحفوظة
      const schemaManager = SchemaManager.getInstance();
      const savedConnection = await schemaManager.loadConnection();

      if (savedConnection) {
        await agentInstance.initialize(savedConnection);
      }
    }

    const state = agentInstance.getState();
    const tablesInfo = agentInstance.getTablesInfo();

    return NextResponse.json({
      success: true,
      state,
      tablesInfo,
      tablesCount: tablesInfo.length
    });

  } catch (error) {
    console.error('خطأ في الحصول على حالة الوكيل:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}
