import { NextResponse } from 'next/server';
import { MSSQLSchemaExtractor } from '@/lib/database/mssql-extractor';

export async function POST() {
  try {
    console.log('🚀 بدء اختبار الاتصال المباشر...');
    
    const extractor = new MSSQLSchemaExtractor();
    const success = await extractor.testDirectConnection();
    
    if (success) {
      return NextResponse.json({ 
        success: true, 
        message: '✅ نجح الاتصال المباشر بالبيانات المقدمة!' 
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        error: '❌ فشل الاتصال المباشر. تحقق من console للتفاصيل.' 
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('💥 خطأ في اختبار الاتصال المباشر:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 });
  }
}
