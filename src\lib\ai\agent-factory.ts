// مصنع الوكيل الذكي - يحول قاعدة البيانات إلى وكيل ذكي

import { IntelligentSQLAgent, DatabaseSchema, TableSchema, ColumnSchema } from './intelligent-sql-agent';

/**
 * مصنع لإنشاء وكيل ذكي من قاعدة بيانات
 */
export class AgentFactory {
  
  /**
   * تحليل قاعدة البيانات وإنشاء وكيل ذكي
   */
  static async createFromDatabase(
    connectionString: string, 
    llmClient: any
  ): Promise<IntelligentSQLAgent> {
    
    console.log('🔍 تحليل بنية قاعدة البيانات...');
    
    // تحليل الجداول والأعمدة
    const rawSchema = await this.analyzeDatabase(connectionString);
    
    // توليد أوصاف ذكية للجداول
    const intelligentSchema = await this.generateIntelligentDescriptions(rawSchema, llmClient);
    
    // إنشاء الوكيل الذكي
    const agent = new IntelligentSQLAgent(intelligentSchema, llmClient);
    
    console.log('✅ تم إنشاء الوكيل الذكي بنجاح');
    return agent;
  }

  /**
   * تحليل بنية قاعدة البيانات الخام
   */
  private static async analyzeDatabase(connectionString: string): Promise<any> {
    // هنا سيتم الاتصال بقاعدة البيانات وتحليل البنية
    // يمكن استخدام مكتبات مثل mysql2, pg, mssql حسب نوع قاعدة البيانات
    
    // مثال مؤقت - في التطبيق الحقيقي سيتم قراءة البيانات من قاعدة البيانات
    return {
      tables: [
        {
          name: 'customers',
          columns: [
            { name: 'customer_id', type: 'int', nullable: false, isPrimaryKey: true },
            { name: 'customer_name', type: 'varchar', nullable: false },
            { name: 'email', type: 'varchar', nullable: true },
            { name: 'phone', type: 'varchar', nullable: true },
            { name: 'address', type: 'text', nullable: true }
          ]
        },
        {
          name: 'items',
          columns: [
            { name: 'item_id', type: 'int', nullable: false, isPrimaryKey: true },
            { name: 'item_name', type: 'varchar', nullable: false },
            { name: 'item_code', type: 'varchar', nullable: true },
            { name: 'unit_price', type: 'decimal', nullable: false },
            { name: 'category', type: 'varchar', nullable: true }
          ]
        },
        {
          name: 'invoices',
          columns: [
            { name: 'invoice_id', type: 'int', nullable: false, isPrimaryKey: true },
            { name: 'customer_id', type: 'int', nullable: false, isForeignKey: true },
            { name: 'invoice_date', type: 'datetime', nullable: false },
            { name: 'total_amount', type: 'decimal', nullable: false }
          ]
        },
        {
          name: 'invoice_details',
          columns: [
            { name: 'invoice_detail_id', type: 'int', nullable: false, isPrimaryKey: true },
            { name: 'invoice_id', type: 'int', nullable: false, isForeignKey: true },
            { name: 'item_id', type: 'int', nullable: false, isForeignKey: true },
            { name: 'quantity', type: 'int', nullable: false },
            { name: 'unit_price', type: 'decimal', nullable: false }
          ]
        }
      ],
      relationships: [
        {
          fromTable: 'invoices',
          fromColumn: 'customer_id',
          toTable: 'customers',
          toColumn: 'customer_id',
          type: 'many-to-one'
        },
        {
          fromTable: 'invoice_details',
          fromColumn: 'invoice_id',
          toTable: 'invoices',
          toColumn: 'invoice_id',
          type: 'many-to-one'
        },
        {
          fromTable: 'invoice_details',
          fromColumn: 'item_id',
          toTable: 'items',
          toColumn: 'item_id',
          type: 'many-to-one'
        }
      ]
    };
  }

  /**
   * توليد أوصاف ذكية للجداول والأعمدة
   */
  private static async generateIntelligentDescriptions(
    rawSchema: any, 
    llmClient: any
  ): Promise<DatabaseSchema> {
    
    console.log('🧠 توليد أوصاف ذكية للجداول...');
    
    const intelligentTables: TableSchema[] = [];
    
    for (const table of rawSchema.tables) {
      const intelligentTable = await this.analyzeTable(table, rawSchema, llmClient);
      intelligentTables.push(intelligentTable);
    }
    
    return {
      tables: intelligentTables
    };
  }

  /**
   * تحليل جدول واحد وتوليد وصف ذكي له
   */
  private static async analyzeTable(
    table: any, 
    fullSchema: any, 
    llmClient: any
  ): Promise<TableSchema> {
    
    const prompt = `
🧠 **مهمة: تحليل ذكي لجدول قاعدة البيانات**

**اسم الجدول:** ${table.name}

**الأعمدة:**
${table.columns.map((col: any) => `- ${col.name} (${col.type}) ${col.isPrimaryKey ? '[PK]' : ''} ${col.isForeignKey ? '[FK]' : ''}`).join('\n')}

**العلاقات:**
${fullSchema.relationships
  .filter((rel: any) => rel.fromTable === table.name || rel.toTable === table.name)
  .map((rel: any) => `- ${rel.fromTable}.${rel.fromColumn} → ${rel.toTable}.${rel.toColumn}`)
  .join('\n')}

قم بتحليل هذا الجدول وتوليد:

1. **وصف ذكي للجدول:** ما الغرض من هذا الجدول؟ ما نوع البيانات التي يخزنها؟
2. **وصف لكل عمود:** ما الغرض من كل عمود؟ ما نوع البيانات التي يحتويها؟
3. **السياق التجاري:** كيف يستخدم هذا الجدول في العمليات التجارية؟
4. **الكلمات المفتاحية:** ما الكلمات التي قد يستخدمها المستخدم للإشارة إلى هذا الجدول؟

أرجع النتيجة في JSON:
{
  "tableDescription": "...",
  "businessContext": "...",
  "keywords": [...],
  "columns": [
    {
      "name": "...",
      "description": "...",
      "businessMeaning": "...",
      "searchTerms": [...]
    }
  ]
}
`;

    try {
      const response = await llmClient.generateContent(prompt);
      const analysis = JSON.parse(response);
      
      // تحويل النتيجة إلى TableSchema
      const intelligentColumns: ColumnSchema[] = table.columns.map((col: any) => {
        const colAnalysis = analysis.columns.find((c: any) => c.name === col.name) || {};
        
        return {
          name: col.name,
          type: col.type,
          description: colAnalysis.description || `عمود ${col.name}`,
          nullable: col.nullable || false,
          isPrimaryKey: col.isPrimaryKey || false,
          isForeignKey: col.isForeignKey || false
        };
      });

      return {
        name: table.name,
        description: analysis.tableDescription || `جدول ${table.name}`,
        columns: intelligentColumns,
        relationships: fullSchema.relationships.filter((rel: any) => 
          rel.fromTable === table.name || rel.toTable === table.name
        )
      };
      
    } catch (error) {
      console.error('خطأ في تحليل الجدول:', error);
      
      // fallback - وصف أساسي
      return {
        name: table.name,
        description: `جدول ${table.name} في قاعدة البيانات`,
        columns: table.columns.map((col: any) => ({
          name: col.name,
          type: col.type,
          description: `عمود ${col.name} من نوع ${col.type}`,
          nullable: col.nullable || false,
          isPrimaryKey: col.isPrimaryKey || false,
          isForeignKey: col.isForeignKey || false
        })),
        relationships: fullSchema.relationships.filter((rel: any) => 
          rel.fromTable === table.name || rel.toTable === table.name
        )
      };
    }
  }

  /**
   * إنشاء وكيل من schema موجود
   */
  static createFromSchema(schema: DatabaseSchema, llmClient: any): IntelligentSQLAgent {
    return new IntelligentSQLAgent(schema, llmClient);
  }

  /**
   * تحديث أوصاف الجداول
   */
  static async updateDescriptions(
    agent: IntelligentSQLAgent, 
    llmClient: any
  ): Promise<void> {
    // إعادة تحليل وتحديث الأوصاف
    console.log('🔄 تحديث أوصاف الجداول...');
    // يمكن تنفيذ هذا لاحقاً حسب الحاجة
  }
}
