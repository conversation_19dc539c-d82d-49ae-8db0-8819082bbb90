import fs from 'fs/promises';
import path from 'path';
import { AIProvider } from './ai-provider-manager';

const DATA_DIR = path.join(process.cwd(), 'data');
const AI_PROVIDER_FILE = path.join(DATA_DIR, 'ai-provider.json');

/**
 * حفظ النموذج المختار في الخادم
 */
export async function saveSelectedProvider(provider: AIProvider): Promise<void> {
  try {
    // إنشاء مجلد البيانات إذا لم يكن موجوداً
    await fs.mkdir(DATA_DIR, { recursive: true });
    
    const data = {
      provider,
      savedAt: new Date().toISOString()
    };
    
    await fs.writeFile(AI_PROVIDER_FILE, JSON.stringify(data, null, 2), 'utf-8');
    console.log(`✅ تم حفظ النموذج المختار: ${provider}`);
  } catch (error) {
    console.error('❌ خطأ في حفظ النموذج المختار:', error);
  }
}

/**
 * تحميل النموذج المختار من الخادم
 */
export async function loadSelectedProvider(): Promise<AIProvider | null> {
  try {
    const data = await fs.readFile(AI_PROVIDER_FILE, 'utf-8');
    const parsed = JSON.parse(data);
    console.log(`✅ تم تحميل النموذج المحفوظ: ${parsed.provider}`);
    return parsed.provider;
  } catch (error) {
    console.log('ℹ️ لم يتم العثور على نموذج محفوظ');
    return null;
  }
}

/**
 * حذف النموذج المحفوظ
 */
export async function clearSelectedProvider(): Promise<void> {
  try {
    await fs.unlink(AI_PROVIDER_FILE);
    console.log('✅ تم حذف النموذج المحفوظ');
  } catch (error) {
    console.log('ℹ️ لا يوجد نموذج محفوظ للحذف');
  }
}
