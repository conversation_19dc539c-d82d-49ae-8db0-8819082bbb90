"use client"

import React, { useState, useRef } from 'react';
import { Send, Loader2, Mic, MicOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

export function ChatInput({ 
  onSendMessage, 
  isLoading = false, 
  disabled = false,
  placeholder = "اكتب سؤالك هنا...",
  className = ""
}: ChatInputProps) {
  const [message, setMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isLoading && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    
    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  };

  // Voice input functionality (placeholder for future implementation)
  const toggleVoiceInput = () => {
    setIsListening(!isListening);
    // TODO: Implement voice recognition
  };

  const suggestedQuestions = [
    "ما هي الجداول الموجودة في قاعدة البيانات؟",
    "أظهر لي إجمالي المبيعات هذا الشهر",
    "كم عدد العملاء النشطين؟",
    "ما هي أكثر المنتجات مبيعاً؟"
  ];

  return (
    <div className={cn("bg-white border-t border-gray-200", className)}>
      {/* Suggested Questions */}
      {message === '' && !isLoading && (
        <div className="p-4 border-b border-gray-100">
          <p className="text-sm text-gray-600 mb-3">أسئلة مقترحة:</p>
          <div className="flex flex-wrap gap-2">
            {suggestedQuestions.map((question, index) => (
              <button
                key={index}
                onClick={() => setMessage(question)}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                disabled={disabled}
              >
                {question}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input Form */}
      <form onSubmit={handleSubmit} className="p-4">
        <div className="flex items-end gap-3">
          {/* Voice Input Button */}
          <button
            type="button"
            onClick={toggleVoiceInput}
            disabled={disabled || isLoading}
            className={cn(
              "flex-shrink-0 p-2 rounded-lg transition-colors",
              isListening 
                ? "bg-red-100 text-red-600 hover:bg-red-200" 
                : "bg-gray-100 text-gray-600 hover:bg-gray-200",
              (disabled || isLoading) && "opacity-50 cursor-not-allowed"
            )}
          >
            {isListening ? (
              <MicOff className="w-5 h-5" />
            ) : (
              <Mic className="w-5 h-5" />
            )}
          </button>

          {/* Text Input */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled || isLoading}
              className={cn(
                "w-full px-4 py-3 border border-gray-300 rounded-lg resize-none",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "placeholder-gray-500 text-gray-900",
                "min-h-[48px] max-h-[120px]",
                (disabled || isLoading) && "opacity-50 cursor-not-allowed bg-gray-50"
              )}
              rows={1}
            />
            
            {/* Character count */}
            {message.length > 0 && (
              <div className="absolute bottom-1 left-2 text-xs text-gray-400">
                {message.length}/1000
              </div>
            )}
          </div>

          {/* Send Button */}
          <button
            type="submit"
            disabled={!message.trim() || isLoading || disabled}
            className={cn(
              "flex-shrink-0 p-3 rounded-lg transition-colors",
              "bg-blue-600 text-white hover:bg-blue-700",
              "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-blue-600"
            )}
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Loading indicator */}
        {isLoading && (
          <div className="mt-3 flex items-center justify-center text-sm text-gray-600">
            <Loader2 className="w-4 h-4 animate-spin ml-2" />
            جاري معالجة سؤالك...
          </div>
        )}
      </form>
    </div>
  );
}
