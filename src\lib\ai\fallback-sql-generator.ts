// نظام fallback ذكي وعام لتوليد استعلامات SQL

export interface FallbackSQLResult {
  query: string;
  explanation: string;
  confidence: number;
  isFallback: boolean;
}

// أنماط ذكية عامة تعمل مع أي قاعدة بيانات
interface SmartPattern {
  intent: string;
  keywords: string[];
  generateQuery: (tables: any[], userQuestion: string) => string | null;
  explanation: string;
}

const SMART_PATTERNS: SmartPattern[] = [
  {
    intent: 'search_by_name',
    keywords: ['مبيعات', 'بحث', 'عن'],
    generateQuery: (tables, userQuestion) => {
      // البحث عن جداول تحتوي على name أو اسم
      const nameTable = tables.find(t =>
        t.columns.some((col: any) =>
          col.name.toLowerCase().includes('name') ||
          col.name.includes('اسم') ||
          col.name.includes('item') ||
          col.name.includes('product')
        )
      );

      if (!nameTable) return null;

      const nameColumn = nameTable.columns.find((col: any) =>
        col.name.toLowerCase().includes('name') ||
        col.name.includes('اسم') ||
        col.name.includes('item') ||
        col.name.includes('product')
      );

      // استخراج كلمة البحث
      const searchTerm = extractSearchTerm(userQuestion);
      if (!searchTerm) return null;

      return `SELECT * FROM ${nameTable.name} WHERE ${nameColumn.name} LIKE '%${searchTerm}%' ORDER BY ${nameColumn.name};`;
    },
    explanation: 'بحث ذكي في الجداول بناءً على الاسم'
  },
  {
    intent: 'top_records',
    keywords: ['أكثر', 'أعلى', 'أفضل', 'ترتيب'],
    generateQuery: (tables, userQuestion) => {
      // البحث عن جداول تحتوي على أعمدة رقمية للترتيب
      const numericTable = tables.find(t =>
        t.columns.some((col: any) =>
          col.type.includes('int') ||
          col.type.includes('decimal') ||
          col.type.includes('float') ||
          col.type.includes('number')
        )
      );

      if (!numericTable) return null;

      const numericColumn = numericTable.columns.find((col: any) =>
        col.type.includes('int') ||
        col.type.includes('decimal') ||
        col.type.includes('float')
      );

      const nameColumn = numericTable.columns.find((col: any) =>
        col.name.toLowerCase().includes('name') ||
        col.name.includes('اسم')
      ) || numericTable.columns[0];

      return `SELECT ${nameColumn.name}, ${numericColumn.name} FROM ${numericTable.name} ORDER BY ${numericColumn.name} DESC LIMIT 10;`;
    },
    explanation: 'عرض أعلى السجلات بناءً على القيم الرقمية'
  },
  {
    intent: 'count_records',
    keywords: ['عدد', 'كم', 'إحصائية', 'مجموع'],
    generateQuery: (tables, userQuestion) => {
      // اختيار أول جدول متاح
      const table = tables[0];
      if (!table) return null;

      return `SELECT COUNT(*) as total_count FROM ${table.name};`;
    },
    explanation: 'عد إجمالي السجلات في الجدول'
  }
];



// دالة لاستخراج كلمة البحث من السؤال
function extractSearchTerm(userQuestion: string): string | null {
  // البحث عن كلمة بعد "مبيعات" أو "بحث عن"
  const patterns = [
    /مبيعات\s+(\w+)/,
    /بحث\s+عن\s+(\w+)/,
    /عرض\s+(\w+)/,
    /أين\s+(\w+)/
  ];

  for (const pattern of patterns) {
    const match = userQuestion.match(pattern);
    if (match) return match[1];
  }

  return null;
}

// الدالة الرئيسية لتوليد SQL fallback ذكي
export function generateFallbackSQL(userQuestion: string, availableTables?: any[]): FallbackSQLResult | null {
  console.log('محاولة توليد استعلام SQL باستخدام نظام fallback ذكي...');

  if (!availableTables || availableTables.length === 0) {
    console.log('لا توجد جداول متاحة للتحليل');
    return null;
  }

  // البحث عن أفضل نمط ذكي
  for (const pattern of SMART_PATTERNS) {
    const hasKeywords = pattern.keywords.some(keyword =>
      userQuestion.toLowerCase().includes(keyword)
    );

    if (hasKeywords) {
      const query = pattern.generateQuery(availableTables, userQuestion);
      if (query) {
        console.log('تم توليد استعلام fallback ذكي بنجاح');
        return {
          query: cleanupQuery(query),
          explanation: `${pattern.explanation}\n\n⚠️ تم توليد هذا الاستعلام باستخدام نظام fallback ذكي.`,
          confidence: 0.6,
          isFallback: true
        };
      }
    }
  }

  console.log('لم يتم العثور على نمط مناسب في نظام fallback');
  return null;
}

// دالة للتحقق من صحة الاستعلام الأساسية
export function validateBasicSQL(query: string): boolean {
  // تحققات أساسية
  const hasSelect = query.toLowerCase().includes('select');
  const hasFrom = query.toLowerCase().includes('from');
  const hasValidTables = query.includes('customers') || query.includes('invoices') || query.includes('items');
  
  return hasSelect && hasFrom && hasValidTables;
}

// دالة لتنظيف الاستعلام
export function cleanupQuery(query: string): string {
  return query
    .replace(/\s+/g, ' ') // إزالة المسافات الزائدة
    .replace(/;\s*$/, ';') // التأكد من وجود فاصلة منقوطة في النهاية
    .trim();
}
