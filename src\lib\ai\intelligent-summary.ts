// نظام الملخص الذكي والتحليل المتقدم للبيانات

export interface DataInsight {
  type: 'trend' | 'pattern' | 'anomaly' | 'recommendation' | 'comparison';
  title: string;
  description: string;
  importance: 'high' | 'medium' | 'low';
  data?: any;
}

export interface IntelligentSummary {
  narrative: string;
  keyFindings: string[];
  insights: DataInsight[];
  recommendations: string[];
  trends: string[];
  patterns: string[];
  nextSteps: string[];
}

/**
 * نظام ذكي لتحليل البيانات وتوليد ملخصات سردية
 */
export class IntelligentSummaryGenerator {
  private llmClient: any;

  constructor(llmClient: any) {
    this.llmClient = llmClient;
  }

  /**
   * توليد ملخص ذكي شامل للبيانات
   */
  async generateIntelligentSummary(
    data: any[],
    query: string,
    context?: {
      tableName?: string;
      columns?: string[];
      totalRecords?: number;
    }
  ): Promise<IntelligentSummary> {
    console.log('🧠 توليد ملخص ذكي للبيانات...');

    if (!data || data.length === 0) {
      return this.generateEmptyDataSummary(query);
    }

    // استخدام ملخص متوازن (ليس مفصل جداً وليس بسيط جداً)
    return this.generateBalancedSummary(data, query, context);
  }

  /**
   * توليد ملخص متوازن (الدالة الرئيسية)
   */
  private async generateBalancedSummary(
    data: any[],
    query: string,
    context?: any
  ): Promise<IntelligentSummary> {
    console.log('📊 توليد ملخص متوازن...');

    // تحليل بنية البيانات
    const analysis = this.analyzeDataStructure(data);

    // توليد السرد الذكي
    const narrative = await this.generateNarrative(data, query, analysis, context);

    // استخراج النقاط الرئيسية من السرد
    const keyFindings = this.extractKeyFindings(narrative);

    // توليد رؤى بسيطة
    const insights = this.generateFallbackInsights(data, analysis);

    // توصيات بسيطة
    const recommendations = [
      'راجع النتائج بانتظام لمتابعة التطورات',
      'قم بتحليل الأنماط الزمنية للحصول على رؤى أعمق'
    ];

    return {
      narrative,
      keyFindings,
      insights,
      recommendations,
      trends: await this.analyzeTrends(data, analysis),
      patterns: await this.discoverPatterns(data, analysis),
      nextSteps: await this.suggestNextSteps(query, insights, recommendations)
    };
  }

  /**
   * توليد ملخص سريع بدون AI (لتوفير نقاط API)
   */
  private generateQuickSummary(
    data: any[],
    _query: string,
    _context?: any
  ): IntelligentSummary {
    const count = data.length;
    const firstItem = data[0];
    const columns = Object.keys(firstItem);

    // ملخص بسيط بدون AI
    const narrative = `تم العثور على ${count} نتيجة للاستعلام.`;

    const keyFindings = [
      `إجمالي النتائج: ${count}`,
      `الأعمدة: ${columns.join(', ')}`
    ];

    const recommendations = [
      'راجع النتائج للحصول على المزيد من التفاصيل'
    ];

    return {
      narrative,
      keyFindings,
      insights: [],
      recommendations,
      trends: [],
      patterns: [],
      nextSteps: []
    };
  }

  /**
   * تحليل بنية البيانات
   */
  private analyzeDataStructure(data: any[]): any {
    const sample = data[0];
    const columns = Object.keys(sample);

    const analysis = {
      totalRecords: data.length,
      columns: columns,
      numericColumns: [] as string[],
      textColumns: [] as string[],
      dateColumns: [] as string[],
      statistics: {} as any
    };

    // تحليل أنواع الأعمدة
    for (const column of columns) {
      const sampleValue = sample[column];

      if (typeof sampleValue === 'number' || !isNaN(Number(sampleValue))) {
        analysis.numericColumns.push(column);

        // حساب إحصائيات للأعمدة الرقمية
        const values = data.map(row => Number(row[column])).filter(val => !isNaN(val));
        analysis.statistics[column] = {
          min: Math.min(...values),
          max: Math.max(...values),
          avg: values.reduce((sum, val) => sum + val, 0) / values.length,
          sum: values.reduce((sum, val) => sum + val, 0)
        };
      } else if (this.isDateColumn(column, sampleValue)) {
        analysis.dateColumns.push(column);
      } else {
        analysis.textColumns.push(column);
      }
    }

    return analysis;
  }

  /**
   * توليد السرد الذكي
   */
  private async generateNarrative(
    data: any[],
    query: string,
    analysis: any,
    context?: any
  ): Promise<string> {
    // تحديد نوع الاستعلام لتخصيص السرد
    const isComparison = query.toLowerCase().includes('مقارنة') || query.toLowerCase().includes('بين');
    const isSpecificProduct = this.extractSpecificProducts(query);

    const prompt = `
📊 **مهمة: توليد سرد ذكي ومركز للبيانات**

**السؤال الأصلي:** "${query}"
**نوع الاستعلام:** ${isComparison ? 'مقارنة' : 'تحليل عام'}
**المنتجات المحددة:** ${isSpecificProduct.length > 0 ? isSpecificProduct.join(', ') : 'غير محدد'}

**البيانات المسترجعة:**
عدد السجلات: ${analysis.totalRecords}
الأعمدة: ${analysis.columns.join(', ')}

**البيانات الفعلية (فقط النتائج المطلوبة):**
${JSON.stringify(data, null, 2)}

**الإحصائيات:**
${JSON.stringify(analysis.statistics, null, 2)}

⚠️ **مهم جداً:** ركز فقط على البيانات المعروضة أعلاه. لا تذكر منتجات أو بيانات غير موجودة في النتائج.

اكتب سرداً ذكياً ومركزاً يحلل هذه البيانات المحددة فقط:

1. **ركز على النتائج الفعلية فقط** - لا تذكر منتجات غير موجودة
2. **استخدم الأرقام الدقيقة من البيانات**
3. **إذا كان استعلام مقارنة، قارن بين العناصر الموجودة فقط**
4. **اذكر أسماء المنتجات كما هي في البيانات**
5. **لا تضيف معلومات من خارج البيانات المعروضة**

أكتب السرد فقط بدون أي تنسيق إضافي:
`;

    try {
      const response = await this.llmClient.generateContent(prompt);
      return this.cleanTextResponse(response);
    } catch (error) {
      console.error('خطأ في توليد السرد:', error);
      return this.generateFallbackNarrative(data, query, analysis);
    }
  }

  /**
   * استخراج الرؤى والأنماط
   */
  private async extractInsights(data: any[], query: string, analysis: any): Promise<DataInsight[]> {
    const prompt = `
🔍 **مهمة: استخراج الرؤى والأنماط من البيانات**

**البيانات:**
${JSON.stringify(data.slice(0, 10), null, 2)}

**الإحصائيات:**
${JSON.stringify(analysis.statistics, null, 2)}

استخرج أهم الرؤى والأنماط من هذه البيانات. ركز على:

1. **الاتجاهات:** هل هناك اتجاه صاعد أم هابط؟
2. **الأنماط:** هل هناك تكرار أو نمط معين؟
3. **الشذوذ:** هل هناك قيم غير عادية؟
4. **المقارنات:** كيف تتقارن القيم المختلفة؟
5. **التوصيات:** ما الإجراءات المقترحة؟

أرجع النتيجة في JSON:
{
  "insights": [
    {
      "type": "trend|pattern|anomaly|recommendation|comparison",
      "title": "عنوان الرؤية",
      "description": "وصف مفصل",
      "importance": "high|medium|low"
    }
  ]
}
`;

    try {
      const response = await this.llmClient.generateContent(prompt);
      const result = this.parseJSONResponse(response);
      return result.insights || [];
    } catch (error) {
      console.error('خطأ في استخراج الرؤى:', error);
      return this.generateFallbackInsights(data, analysis);
    }
  }

  /**
   * توليد التوصيات
   */
  private async generateRecommendations(
    data: any[],
    query: string,
    insights: DataInsight[]
  ): Promise<string[]> {
    const prompt = `
💡 **مهمة: توليد توصيات عملية**

**السؤال:** "${query}"
**الرؤى المستخرجة:** ${JSON.stringify(insights, null, 2)}

بناءً على البيانات والرؤى، اقترح توصيات عملية وقابلة للتنفيذ:

1. **توصيات قصيرة المدى** (يمكن تنفيذها خلال أسبوع)
2. **توصيات متوسطة المدى** (خلال شهر)
3. **توصيات طويلة المدى** (خلال ربع سنة)

أرجع النتيجة في JSON:
{
  "recommendations": [
    "توصية عملية وواضحة",
    "توصية أخرى قابلة للتنفيذ"
  ]
}
`;

    try {
      const response = await this.llmClient.generateContent(prompt);
      const result = this.parseJSONResponse(response);
      return result.recommendations || [];
    } catch (error) {
      console.error('خطأ في توليد التوصيات:', error);
      return ['راجع البيانات بانتظام لمتابعة التطورات', 'قم بتحليل الأنماط الزمنية للحصول على رؤى أعمق'];
    }
  }

  /**
   * تحليل الاتجاهات
   */
  private async analyzeTrends(data: any[], analysis: any): Promise<string[]> {
    // تحليل بسيط للاتجاهات
    const trends: string[] = [];
    
    for (const column of analysis.numericColumns) {
      const stats = analysis.statistics[column];
      if (stats) {
        if (stats.max > stats.avg * 2) {
          trends.push(`${column} يظهر تفاوتاً كبيراً في القيم`);
        }
        if (stats.min === 0) {
          trends.push(`${column} يحتوي على قيم صفرية`);
        }
      }
    }

    return trends;
  }

  /**
   * اكتشاف الأنماط
   */
  private async discoverPatterns(data: any[], analysis: any): Promise<string[]> {
    const patterns: string[] = [];
    
    // تحليل التوزيع
    if (data.length > 1) {
      patterns.push(`البيانات تحتوي على ${data.length} عنصر`);
      
      // تحليل التكرار
      for (const column of analysis.textColumns) {
        const values = data.map(row => row[column]);
        const uniqueValues = [...new Set(values)];
        if (uniqueValues.length < values.length * 0.8) {
          patterns.push(`${column} يظهر تكراراً في القيم`);
        }
      }
    }

    return patterns;
  }

  /**
   * اقتراح الخطوات التالية
   */
  private async suggestNextSteps(
    query: string,
    insights: DataInsight[],
    recommendations: string[]
  ): Promise<string[]> {
    return [
      'قم بتحليل البيانات على فترات زمنية مختلفة',
      'ادرس العوامل المؤثرة على النتائج',
      'قارن النتائج مع فترات سابقة',
      'ضع خطة عمل بناءً على التوصيات'
    ];
  }

  /**
   * استخراج النقاط الرئيسية من السرد
   */
  private extractKeyFindings(narrative: string): string[] {
    // استخراج بسيط للنقاط الرئيسية
    const sentences = narrative.split(/[.!?]/).filter(s => s.trim().length > 20);
    return sentences.slice(0, 3).map(s => s.trim());
  }

  /**
   * توليد ملخص للبيانات الفارغة
   */
  private generateEmptyDataSummary(query: string): IntelligentSummary {
    return {
      narrative: `لم يتم العثور على أي بيانات تطابق الاستعلام "${query}". قد يكون السبب في عدم وجود بيانات مطابقة أو الحاجة لتعديل معايير البحث.`,
      keyFindings: ['لا توجد بيانات مطابقة للاستعلام'],
      insights: [{
        type: 'recommendation',
        title: 'لا توجد نتائج',
        description: 'تحقق من صحة معايير البحث أو جرب مصطلحات أخرى',
        importance: 'high'
      }],
      recommendations: [
        'تحقق من صحة أسماء المنتجات أو العملاء',
        'جرب استخدام مصطلحات بحث أوسع',
        'راجع البيانات المتاحة في النظام'
      ],
      trends: [],
      patterns: [],
      nextSteps: ['راجع معايير البحث', 'تحقق من البيانات المتاحة']
    };
  }

  /**
   * توليد سرد احتياطي
   */
  private generateFallbackNarrative(data: any[], query: string, analysis: any): string {
    const topItem = data[0];
    const itemName = Object.values(topItem)[0];
    const itemValue = Object.values(topItem)[1];
    
    return `تُظهر نتائج الاستعلام "${query}" معلومات قيمة حول البيانات المطلوبة. تم العثور على ${analysis.totalRecords} سجل، حيث يتصدر "${itemName}" القائمة بقيمة ${itemValue}. هذه النتائج تقدم نظرة شاملة على الوضع الحالي وتساعد في اتخاذ قرارات مدروسة.`;
  }

  /**
   * توليد رؤى احتياطية
   */
  private generateFallbackInsights(data: any[], analysis: any): DataInsight[] {
    const insights: DataInsight[] = [];
    
    if (data.length > 0) {
      insights.push({
        type: 'pattern',
        title: 'حجم البيانات',
        description: `تم العثور على ${data.length} سجل في النتائج`,
        importance: 'medium'
      });
    }

    return insights;
  }

  /**
   * استخراج أسماء المنتجات المحددة من السؤال
   */
  private extractSpecificProducts(query: string): string[] {
    const products: string[] = [];
    const commonProducts = [
      'بط', 'ديك', 'رومي', 'دجاج', 'لحم', 'سمك', 'بيض',
      'برتقال', 'تفاح', 'موز', 'عنب', 'فراولة', 'مانجو',
      'خيار', 'طماطم', 'بصل', 'جزر', 'خس', 'بقدونس',
      'أرز', 'سكر', 'ملح', 'زيت', 'دقيق', 'شاي', 'قهوة'
    ];

    for (const product of commonProducts) {
      if (query.toLowerCase().includes(product)) {
        products.push(product);
      }
    }

    return products;
  }

  /**
   * تنظيف النص المرجع من LLM
   */
  private cleanTextResponse(response: string): string {
    return response
      .replace(/^```.*$/gm, '') // إزالة code blocks
      .replace(/^\*\*.*\*\*$/gm, '') // إزالة العناوين
      .trim();
  }

  /**
   * تحليل استجابة JSON
   */
  private parseJSONResponse(response: string): any {
    try {
      return JSON.parse(response);
    } catch (error) {
      let cleanResponse = response.trim();
      cleanResponse = cleanResponse.replace(/^```json\s*/i, '');
      cleanResponse = cleanResponse.replace(/^```\s*/i, '');
      cleanResponse = cleanResponse.replace(/\s*```$/i, '');
      
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }
      
      try {
        return JSON.parse(cleanResponse);
      } catch (secondError) {
        console.error('فشل في تحليل JSON:', response);
        return {};
      }
    }
  }

  /**
   * تحديد ما إذا كان العمود يحتوي على تواريخ
   */
  private isDateColumn(columnName: string, sampleValue: any): boolean {
    const dateKeywords = ['date', 'time', 'created', 'updated', 'تاريخ', 'وقت'];
    const nameContainsDate = dateKeywords.some(keyword => 
      columnName.toLowerCase().includes(keyword)
    );
    
    if (nameContainsDate) return true;
    
    // محاولة تحليل القيمة كتاريخ
    if (typeof sampleValue === 'string') {
      const dateValue = new Date(sampleValue);
      return !isNaN(dateValue.getTime());
    }
    
    return false;
  }
}
