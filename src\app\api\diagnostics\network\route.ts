import { NextResponse } from 'next/server';
import { diagnoseNetworkIssues, getProxyConfig } from '@/lib/ai/network-config';

export async function GET() {
  try {
    console.log('🔍 بدء تشخيص مشاكل الشبكة...');

    // تشخيص شامل للشبكة
    const diagnosis = await diagnoseNetworkIssues();
    
    // معلومات إضافية
    const proxyConfig = getProxyConfig();
    const hasProxy = !!(proxyConfig.httpProxy || proxyConfig.httpsProxy);
    
    // معلومات البيئة
    const environmentInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      hasGeminiKey: !!process.env.GEMINI_API_KEY,
      geminiKeyLength: process.env.GEMINI_API_KEY?.length || 0,
      proxy: hasProxy ? proxyConfig : null
    };

    // تحليل المشاكل وتقديم حلول
    const solutions: string[] = [];
    
    if (!diagnosis.internetConnection) {
      solutions.push('🌐 تأكد من اتصالك بالإنترنت');
      solutions.push('🔄 أعد تشغيل الراوتر أو المودم');
      solutions.push('📱 جرب استخدام بيانات الهاتف المحمول');
    }
    
    if (!diagnosis.dnsResolution) {
      solutions.push('🔧 غير إعدادات DNS إلى 8.8.8.8 و 8.8.4.4');
      solutions.push('💻 أعد تشغيل الكمبيوتر');
      solutions.push('🛡️ تحقق من إعدادات الجدار الناري');
    }
    
    if (!diagnosis.geminiAPIAccess) {
      solutions.push('🔑 تحقق من صحة GEMINI_API_KEY');
      solutions.push('🌍 تأكد من أن API متاح في منطقتك');
      solutions.push('⏰ انتظر قليلاً إذا تم تجاوز حد الطلبات');
      
      if (hasProxy) {
        solutions.push('🔄 جرب تعطيل الـ Proxy مؤقتاً');
      }
    }

    // إضافة حلول عامة
    if (diagnosis.recommendations.length > 0) {
      solutions.push(...diagnosis.recommendations.map(rec => `💡 ${rec}`));
    }

    const result = {
      status: 'success',
      diagnosis,
      environment: environmentInfo,
      solutions,
      summary: {
        overallHealth: diagnosis.internetConnection && diagnosis.geminiAPIAccess ? 'جيد' : 'يحتاج إصلاح',
        criticalIssues: [
          !diagnosis.internetConnection && 'لا يوجد اتصال إنترنت',
          !diagnosis.dnsResolution && 'مشكلة في DNS',
          !diagnosis.geminiAPIAccess && 'لا يمكن الوصول لـ Gemini API'
        ].filter(Boolean),
        timestamp: new Date().toISOString()
      }
    };

    console.log('✅ تم تشخيص الشبكة:', result.summary);

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ خطأ في تشخيص الشبكة:', error);
    
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'خطأ غير معروف',
      solutions: [
        '🔄 أعد تحميل الصفحة',
        '💻 أعد تشغيل التطبيق',
        '🌐 تحقق من اتصالك بالإنترنت',
        '🔧 تواصل مع الدعم الفني'
      ],
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// إضافة endpoint لاختبار سريع
export async function POST() {
  try {
    const quickTest = {
      timestamp: new Date().toISOString(),
      geminiKeyExists: !!process.env.GEMINI_API_KEY,
      nodeEnv: process.env.NODE_ENV,
      platform: process.platform
    };

    // اختبار سريع للاتصال
    try {
      const testResponse = await fetch('https://www.google.com', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      quickTest.internetConnection = testResponse.ok;
    } catch {
      quickTest.internetConnection = false;
    }

    return NextResponse.json({
      status: 'success',
      quickTest,
      message: quickTest.internetConnection ? 
        '✅ الاتصال يعمل بشكل طبيعي' : 
        '❌ مشكلة في الاتصال'
    });

  } catch (error) {
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'خطأ في الاختبار السريع'
    }, { status: 500 });
  }
}
