# دليل حل مشاكل الشبكة والاتصال

## المشاكل الشائعة وحلولها

### 1. خط<PERSON> "fetch failed sending request"

هذا الخطأ يشير إلى مشكلة في الاتصال بـ Google Gemini API. إليك الحلول المقترحة:

#### الحلول السريعة:
```bash
# 1. تحقق من اتصالك بالإنترنت
ping google.com

# 2. تحقق من DNS
nslookup generativelanguage.googleapis.com

# 3. اختبر الاتصال بـ API
curl -H "Authorization: Bearer YOUR_API_KEY" \
  https://generativelanguage.googleapis.com/v1beta/models
```

#### الحلول المتقدمة:

**أ. تغيير DNS:**
- Windows: اذهب إلى إعدادات الشبكة وغير DNS إلى `*******` و `*******`
- macOS/Linux: 
  ```bash
  sudo echo "nameserver *******" > /etc/resolv.conf
  sudo echo "nameserver *******" >> /etc/resolv.conf
  ```

**ب. إعدادات Proxy:**
إذا كنت تستخدم proxy، أضف المتغيرات التالية:
```bash
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=https://your-proxy:port
export NO_PROXY=localhost,127.0.0.1
```

**ج. إعدادات الجدار الناري:**
تأكد من أن الجدار الناري يسمح بالاتصالات الخارجية على المنافذ 80 و 443.

### 2. مشاكل API Key

#### التحقق من صحة المفتاح:
```javascript
// في المتصفح، افتح Developer Tools واكتب:
fetch('/api/diagnostics/network')
  .then(r => r.json())
  .then(console.log);
```

#### الحصول على مفتاح جديد:
1. اذهب إلى [Google AI Studio](https://aistudio.google.com/apikey)
2. أنشئ مفتاح API جديد
3. انسخ المفتاح وضعه في `.env.local`:
   ```
   GEMINI_API_KEY=your_new_api_key_here
   ```

### 3. مشاكل الشبكة في البيئات المختلفة

#### Windows:
```cmd
# إعادة تعيين إعدادات الشبكة
ipconfig /flushdns
ipconfig /release
ipconfig /renew
netsh winsock reset
```

#### macOS:
```bash
# إعادة تعيين DNS
sudo dscacheutil -flushcache
sudo killall -HUP mDNSResponder
```

#### Linux:
```bash
# إعادة تشغيل خدمة الشبكة
sudo systemctl restart networking
# أو
sudo service network-manager restart
```

### 4. مشاكل في بيئة التطوير

#### Node.js Issues:
```bash
# تحديث Node.js
nvm install --lts
nvm use --lts

# إعادة تثبيت dependencies
rm -rf node_modules package-lock.json
npm install
```

#### Next.js Issues:
```bash
# مسح cache
rm -rf .next
npm run build
npm run dev
```

### 5. استخدام أدوات التشخيص المدمجة

#### في التطبيق:
1. اذهب إلى صفحة الإعدادات
2. اضغط على "تشخيص الشبكة"
3. اتبع التوصيات المعروضة

#### عبر API:
```bash
# فحص شامل
curl http://localhost:3000/api/diagnostics/network

# فحص سريع
curl -X POST http://localhost:3000/api/diagnostics/network
```

## إعدادات متقدمة

### 1. تخصيص إعدادات الشبكة

أنشئ ملف `network.config.js` في جذر المشروع:
```javascript
module.exports = {
  timeout: 60000, // 60 ثانية
  retries: 5,
  retryDelay: 3000,
  proxy: process.env.HTTP_PROXY,
  dns: ['*******', '*******']
};
```

### 2. إعدادات البيئة المتقدمة

في `.env.local`:
```env
# إعدادات الشبكة
NETWORK_TIMEOUT=60000
MAX_RETRIES=5
RETRY_DELAY=3000

# إعدادات Proxy (اختيارية)
HTTP_PROXY=http://proxy.company.com:8080
HTTPS_PROXY=https://proxy.company.com:8080
NO_PROXY=localhost,127.0.0.1,.local

# إعدادات DNS (اختيارية)
DNS_SERVERS=*******,*******
```

### 3. مراقبة الأداء

```javascript
// إضافة مراقبة مخصصة
const monitor = {
  logRequest: (url, duration) => {
    console.log(`Request to ${url} took ${duration}ms`);
  },
  
  logError: (error, context) => {
    console.error(`Error in ${context}:`, error);
  }
};
```

## نصائح للأداء الأمثل

### 1. تحسين الطلبات:
- استخدم cache للاستعلامات المتكررة
- قلل من عدد الطلبات المتزامنة
- استخدم compression للبيانات الكبيرة

### 2. إدارة الأخطاء:
- اعرض رسائل خطأ واضحة للمستخدم
- سجل الأخطاء للمراجعة اللاحقة
- وفر خيارات بديلة عند فشل API

### 3. مراقبة الحالة:
- فحص دوري لحالة API
- تنبيهات عند حدوث مشاكل
- إحصائيات الأداء

## الحصول على المساعدة

إذا استمرت المشاكل:

1. **تحقق من السجلات:**
   ```bash
   # في المتصفح
   F12 → Console → Network

   # في الخادم
   npm run dev
   # راقب الرسائل في Terminal
   ```

2. **جرب الحلول البديلة:**
   - استخدم VPN إذا كان API محجوب في منطقتك
   - جرب شبكة مختلفة (مثل بيانات الهاتف)
   - استخدم خادم proxy مختلف

3. **تواصل مع الدعم:**
   - أرفق معلومات النظام
   - أرفق رسائل الخطأ كاملة
   - اذكر الخطوات التي جربتها

## معلومات إضافية

- **وثائق Google Gemini API:** https://ai.google.dev/docs
- **حالة خدمات Google:** https://status.cloud.google.com/
- **مجتمع المطورين:** https://developers.googleblog.com/

---

*آخر تحديث: ديسمبر 2024*
