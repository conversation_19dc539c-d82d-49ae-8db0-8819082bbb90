'use client';

import { useState } from 'react';
import { <PERSON>ert<PERSON><PERSON>cle, CheckCircle, Wifi, WifiOff, <PERSON>fresh<PERSON><PERSON>, Setting<PERSON> } from 'lucide-react';

interface NetworkDiagnosis {
  internetConnection: boolean;
  dnsResolution: boolean;
  geminiAPIAccess: boolean;
  recommendations: string[];
}

interface DiagnosisResult {
  status: string;
  diagnosis: NetworkDiagnosis;
  environment: {
    nodeVersion: string;
    platform: string;
    hasGeminiKey: boolean;
    geminiKeyLength: number;
    proxy: any;
  };
  solutions: string[];
  summary: {
    overallHealth: string;
    criticalIssues: string[];
    timestamp: string;
  };
}

export function NetworkStatus() {
  const [isChecking, setIsChecking] = useState(false);
  const [diagnosis, setDiagnosis] = useState<DiagnosisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const runDiagnosis = async () => {
    setIsChecking(true);
    setError(null);
    
    try {
      const response = await fetch('/api/diagnostics/network');
      const result = await response.json();
      
      if (result.status === 'success') {
        setDiagnosis(result);
      } else {
        setError(result.error || 'فشل في التشخيص');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في الاتصال');
    } finally {
      setIsChecking(false);
    }
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <AlertCircle className="w-5 h-5 text-red-500" />
    );
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'جيد': return 'text-green-600 bg-green-50';
      case 'يحتاج إصلاح': return 'text-red-600 bg-red-50';
      default: return 'text-yellow-600 bg-yellow-50';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Settings className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">تشخيص الشبكة</h3>
        </div>
        
        <button
          onClick={runDiagnosis}
          disabled={isChecking}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <RefreshCw className={`w-4 h-4 ${isChecking ? 'animate-spin' : ''}`} />
          {isChecking ? 'جاري الفحص...' : 'فحص الشبكة'}
        </button>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <span className="text-red-700 font-medium">خطأ في التشخيص</span>
          </div>
          <p className="text-red-600 mt-1">{error}</p>
        </div>
      )}

      {diagnosis && (
        <div className="space-y-4">
          {/* ملخص الحالة العامة */}
          <div className={`p-4 rounded-lg ${getHealthColor(diagnosis.summary.overallHealth)}`}>
            <div className="flex items-center gap-2">
              {diagnosis.summary.overallHealth === 'جيد' ? (
                <Wifi className="w-5 h-5" />
              ) : (
                <WifiOff className="w-5 h-5" />
              )}
              <span className="font-medium">
                الحالة العامة: {diagnosis.summary.overallHealth}
              </span>
            </div>
            {diagnosis.summary.criticalIssues.length > 0 && (
              <div className="mt-2">
                <p className="text-sm font-medium">المشاكل الحرجة:</p>
                <ul className="text-sm mt-1 space-y-1">
                  {diagnosis.summary.criticalIssues.map((issue, index) => (
                    <li key={index} className="flex items-center gap-1">
                      <span className="w-1 h-1 bg-current rounded-full"></span>
                      {issue}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* تفاصيل التشخيص */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(diagnosis.diagnosis.internetConnection)}
                <span className="font-medium">اتصال الإنترنت</span>
              </div>
              <p className="text-sm text-gray-600">
                {diagnosis.diagnosis.internetConnection ? 'متصل' : 'غير متصل'}
              </p>
            </div>

            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(diagnosis.diagnosis.dnsResolution)}
                <span className="font-medium">DNS</span>
              </div>
              <p className="text-sm text-gray-600">
                {diagnosis.diagnosis.dnsResolution ? 'يعمل بشكل طبيعي' : 'مشكلة في الحل'}
              </p>
            </div>

            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(diagnosis.diagnosis.geminiAPIAccess)}
                <span className="font-medium">Gemini API</span>
              </div>
              <p className="text-sm text-gray-600">
                {diagnosis.diagnosis.geminiAPIAccess ? 'متاح' : 'غير متاح'}
              </p>
            </div>
          </div>

          {/* الحلول المقترحة */}
          {diagnosis.solutions.length > 0 && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">الحلول المقترحة:</h4>
              <ul className="space-y-1">
                {diagnosis.solutions.map((solution, index) => (
                  <li key={index} className="text-sm text-blue-800 flex items-start gap-2">
                    <span className="w-1 h-1 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                    <span>{solution}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* معلومات البيئة */}
          <details className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <summary className="font-medium text-gray-900 cursor-pointer">
              معلومات البيئة التقنية
            </summary>
            <div className="mt-2 space-y-1 text-sm text-gray-600">
              <p>إصدار Node.js: {diagnosis.environment.nodeVersion}</p>
              <p>النظام: {diagnosis.environment.platform}</p>
              <p>مفتاح Gemini: {diagnosis.environment.hasGeminiKey ? '✅ موجود' : '❌ غير موجود'}</p>
              {diagnosis.environment.hasGeminiKey && (
                <p>طول المفتاح: {diagnosis.environment.geminiKeyLength} حرف</p>
              )}
              {diagnosis.environment.proxy && (
                <p>Proxy: مُفعل</p>
              )}
            </div>
          </details>

          <p className="text-xs text-gray-500 text-center">
            آخر فحص: {new Date(diagnosis.summary.timestamp).toLocaleString('ar-SA')}
          </p>
        </div>
      )}

      {!diagnosis && !error && !isChecking && (
        <div className="text-center py-8 text-gray-500">
          <Wifi className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>اضغط على "فحص الشبكة" لتشخيص حالة الاتصال</p>
        </div>
      )}
    </div>
  );
}
