"use client"

import React from 'react';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, AlertCircle, Loader2, Database, Brain, Search } from 'lucide-react';
import { SchemaExtractionProgress } from '@/lib/database/types';

interface SchemaProgressProps {
  progress: SchemaExtractionProgress;
  onClose?: () => void;
}

export function SchemaProgress({ progress, onClose }: SchemaProgressProps) {
  const getProgressPercentage = () => {
    if (progress.isComplete) return 100;
    return Math.round((progress.currentStepIndex / progress.totalSteps) * 100);
  };

  const getStepIcon = (stepIndex: number, currentIndex: number, isComplete: boolean, hasError: boolean) => {
    if (hasError && stepIndex === currentIndex) {
      return <AlertCircle className="w-5 h-5 text-red-500" />;
    }
    if (stepIndex < currentIndex || isComplete) {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
    if (stepIndex === currentIndex && !isComplete) {
      return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
    }
    return <div className="w-5 h-5 rounded-full border-2 border-gray-300" />;
  };

  const steps = [
    { name: 'الاتصال بقاعدة البيانات', icon: Database },
    { name: 'استخراج بنية الجداول', icon: Database },
    { name: 'تحليل الجداول بالذكاء الاصطناعي', icon: Brain },
    { name: 'توليد أوصاف الجداول', icon: Brain },
    { name: 'إنشاء فهارس البحث', icon: Search },
    { name: 'حفظ النتائج', icon: CheckCircle }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <h2 className="text-2xl font-bold mb-2">تحليل قاعدة البيانات</h2>
          <p className="text-blue-100">
            {progress.isComplete 
              ? progress.error 
                ? 'حدث خطأ أثناء التحليل'
                : 'تم الانتهاء من التحليل بنجاح'
              : progress.currentStep
            }
          </p>
        </div>

        {/* Progress Bar */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">
              التقدم الإجمالي
            </span>
            <span className="text-sm font-medium text-gray-700">
              {getProgressPercentage()}%
            </span>
          </div>
          <Progress 
            value={getProgressPercentage()} 
            className="h-3"
          />
          
          {progress.totalTables > 0 && (
            <div className="mt-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  معالجة الجداول
                </span>
                <span className="text-sm font-medium text-gray-700">
                  {progress.tablesProcessed} من {progress.totalTables}
                </span>
              </div>
              <Progress 
                value={(progress.tablesProcessed / progress.totalTables) * 100} 
                className="h-2"
              />
            </div>
          )}
        </div>

        {/* Steps */}
        <div className="p-6 border-b max-h-60 overflow-y-auto">
          <h3 className="text-lg font-semibold mb-4">خطوات العملية</h3>
          <div className="space-y-3">
            {steps.map((step, index) => {
              const StepIcon = step.icon;
              return (
                <div 
                  key={index}
                  className={`flex items-center space-x-3 space-x-reverse p-3 rounded-lg ${
                    index === progress.currentStepIndex 
                      ? 'bg-blue-50 border border-blue-200' 
                      : index < progress.currentStepIndex || progress.isComplete
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-gray-50 border border-gray-200'
                  }`}
                >
                  {getStepIcon(index, progress.currentStepIndex, progress.isComplete, !!progress.error)}
                  <StepIcon className={`w-5 h-5 ${
                    index === progress.currentStepIndex 
                      ? 'text-blue-500' 
                      : index < progress.currentStepIndex || progress.isComplete
                      ? 'text-green-500'
                      : 'text-gray-400'
                  }`} />
                  <span className={`font-medium ${
                    index === progress.currentStepIndex 
                      ? 'text-blue-700' 
                      : index < progress.currentStepIndex || progress.isComplete
                      ? 'text-green-700'
                      : 'text-gray-500'
                  }`}>
                    {step.name}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Logs */}
        <div className="p-6 border-b max-h-40 overflow-y-auto">
          <h3 className="text-lg font-semibold mb-4">سجل العمليات</h3>
          <div className="space-y-2">
            {progress.logs.map((log, index) => (
              <div 
                key={index}
                className="text-sm text-gray-600 bg-gray-50 p-2 rounded border-r-4 border-blue-400"
              >
                {log}
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 bg-gray-50">
          {progress.isComplete ? (
            <div className="flex justify-between items-center">
              {progress.error ? (
                <div className="flex items-center text-red-600">
                  <AlertCircle className="w-5 h-5 ml-2" />
                  <span className="font-medium">فشل في التحليل</span>
                </div>
              ) : (
                <div className="flex items-center text-green-600">
                  <CheckCircle className="w-5 h-5 ml-2" />
                  <span className="font-medium">تم الانتهاء بنجاح</span>
                </div>
              )}
              
              {onClose && (
                <button
                  onClick={onClose}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  إغلاق
                </button>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center">
              <Loader2 className="w-5 h-5 animate-spin ml-2" />
              <span className="text-gray-600">جاري المعالجة...</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
