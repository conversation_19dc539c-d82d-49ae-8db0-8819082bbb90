// إعدادات محسنة لـ Google Gemini API

export interface GeminiConfig {
  model: string;
  temperature: number;
  maxOutputTokens: number;
  topP?: number;
  topK?: number;
  candidateCount?: number;
  stopSequences?: string[];
}

// إعدادات مختلفة لحالات الاستخدام المختلفة
export const GEMINI_CONFIGS = {
  // للاستعلامات SQL - دقة عالية
  SQL_GENERATION: {
    model: 'gemini-1.5-flash',
    temperature: 0.1,
    maxOutputTokens: 1500,
    topP: 0.8,
    topK: 10
  } as GeminiConfig,

  // لوصف الجداول - إبداع متوسط
  TABLE_DESCRIPTION: {
    model: 'gemini-1.5-flash',
    temperature: 0.3,
    maxOutputTokens: 1000,
    topP: 0.9,
    topK: 20
  } as GeminiConfig,

  // للمحتوى العام - متوازن
  GENERAL_CONTENT: {
    model: 'gemini-1.5-flash',
    temperature: 0.1,
    maxOutputTokens: 2048,
    topP: 0.8,
    topK: 15
  } as GeminiConfig,

  // للتحليل السياقي - دقة عالية جداً
  CONTEXT_ANALYSIS: {
    model: 'gemini-1.5-flash',
    temperature: 0.05,
    maxOutputTokens: 1000,
    topP: 0.7,
    topK: 5
  } as GeminiConfig
};

// رسائل خطأ مخصصة باللغة العربية
export const GEMINI_ERROR_MESSAGES = {
  NETWORK_ERROR: 'خطأ في الاتصال بالشبكة - تحقق من اتصالك بالإنترنت',
  API_KEY_INVALID: 'مفتاح API غير صحيح - تحقق من GEMINI_API_KEY',
  QUOTA_EXCEEDED: 'تم تجاوز الحد المسموح من الطلبات - انتظر قليلاً',
  MODEL_NOT_FOUND: 'النموذج المطلوب غير متاح',
  CONTENT_FILTERED: 'تم حجب المحتوى بواسطة مرشحات الأمان',
  TIMEOUT: 'انتهت مهلة الانتظار - جرب مرة أخرى',
  UNKNOWN: 'خطأ غير معروف في Gemini API'
};

// دالة لتحليل نوع الخطأ وإرجاع رسالة مناسبة
export function parseGeminiError(error: any): {
  type: keyof typeof GEMINI_ERROR_MESSAGES;
  message: string;
  isRetryable: boolean;
} {
  const errorMessage = error?.message || error?.toString() || '';
  const errorCode = error?.code || error?.status;

  // أخطاء الشبكة
  if (errorMessage.includes('fetch failed') || 
      errorMessage.includes('ECONNRESET') ||
      errorMessage.includes('ETIMEDOUT') ||
      errorMessage.includes('ENOTFOUND')) {
    return {
      type: 'NETWORK_ERROR',
      message: GEMINI_ERROR_MESSAGES.NETWORK_ERROR,
      isRetryable: true
    };
  }

  // مشاكل API Key
  if (errorCode === 401 || errorCode === 403 || errorMessage.includes('API key')) {
    return {
      type: 'API_KEY_INVALID',
      message: GEMINI_ERROR_MESSAGES.API_KEY_INVALID,
      isRetryable: false
    };
  }

  // تجاوز الحد المسموح
  if (errorCode === 429 || errorMessage.includes('quota') || errorMessage.includes('rate')) {
    return {
      type: 'QUOTA_EXCEEDED',
      message: GEMINI_ERROR_MESSAGES.QUOTA_EXCEEDED,
      isRetryable: true
    };
  }

  // النموذج غير موجود
  if (errorCode === 404 || errorMessage.includes('model')) {
    return {
      type: 'MODEL_NOT_FOUND',
      message: GEMINI_ERROR_MESSAGES.MODEL_NOT_FOUND,
      isRetryable: false
    };
  }

  // مرشحات الأمان
  if (errorMessage.includes('safety') || errorMessage.includes('blocked')) {
    return {
      type: 'CONTENT_FILTERED',
      message: GEMINI_ERROR_MESSAGES.CONTENT_FILTERED,
      isRetryable: false
    };
  }

  // انتهاء المهلة
  if (errorMessage.includes('timeout') || errorMessage.includes('aborted')) {
    return {
      type: 'TIMEOUT',
      message: GEMINI_ERROR_MESSAGES.TIMEOUT,
      isRetryable: true
    };
  }

  // خطأ غير معروف
  return {
    type: 'UNKNOWN',
    message: `${GEMINI_ERROR_MESSAGES.UNKNOWN}: ${errorMessage}`,
    isRetryable: true
  };
}

// دالة لتحسين prompt حسب نوع المهمة
export function optimizePrompt(prompt: string, taskType: 'sql' | 'description' | 'analysis'): string {
  const baseInstructions = {
    sql: `أنت خبير في كتابة استعلامات SQL. يجب أن تكون الإجابة دقيقة ومحسنة.`,
    description: `أنت خبير في تحليل قواعد البيانات. قدم وصفاً واضحاً ومفيداً.`,
    analysis: `أنت محلل بيانات خبير. قدم تحليلاً دقيقاً ومفصلاً.`
  };

  const jsonInstruction = `\n\nيجب أن تكون الإجابة بصيغة JSON صحيحة فقط، بدون أي نص إضافي.`;
  
  return `${baseInstructions[taskType]}\n\n${prompt}${jsonInstruction}`;
}

// دالة للتحقق من صحة الاستجابة
export function validateGeminiResponse(response: any, expectedFields: string[]): {
  isValid: boolean;
  missingFields: string[];
  data?: any;
} {
  if (!response || typeof response !== 'object') {
    return {
      isValid: false,
      missingFields: expectedFields
    };
  }

  const missingFields = expectedFields.filter(field => !(field in response));
  
  return {
    isValid: missingFields.length === 0,
    missingFields,
    data: response
  };
}

// إعدادات إعادة المحاولة المحسنة للسرعة
export const RETRY_CONFIG = {
  maxRetries: 2, // تقليل عدد المحاولات
  baseDelay: 1000, // ثانية واحدة
  maxDelay: 10000, // 10 ثواني كحد أقصى
  backoffMultiplier: 1.5, // تقليل معامل التأخير

  // تأخيرات مخصصة لأنواع مختلفة من الأخطاء
  errorDelays: {
    NETWORK_ERROR: 2000, // تقليل إلى ثانيتين
    QUOTA_EXCEEDED: 8000, // تقليل إلى 8 ثواني
    TIMEOUT: 1500, // تقليل إلى 1.5 ثانية
    UNKNOWN: 1000 // ثانية واحدة
  }
};

// دالة لحساب تأخير إعادة المحاولة
export function calculateRetryDelay(
  attempt: number, 
  errorType: keyof typeof GEMINI_ERROR_MESSAGES
): number {
  const baseDelay = RETRY_CONFIG.errorDelays[errorType] || RETRY_CONFIG.baseDelay;
  const exponentialDelay = baseDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, attempt - 1);
  
  return Math.min(exponentialDelay, RETRY_CONFIG.maxDelay);
}

// دالة للتحقق من حالة API
export async function checkGeminiAPIHealth(): Promise<{
  isHealthy: boolean;
  latency?: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    // محاولة طلب بسيط للتحقق من حالة API
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.GEMINI_API_KEY}`,
      },
      signal: AbortSignal.timeout(10000)
    });

    const latency = Date.now() - startTime;

    if (response.ok) {
      return { isHealthy: true, latency };
    } else {
      return { 
        isHealthy: false, 
        latency,
        error: `HTTP ${response.status}` 
      };
    }
  } catch (error: any) {
    return { 
      isHealthy: false, 
      latency: Date.now() - startTime,
      error: error.message 
    };
  }
}
