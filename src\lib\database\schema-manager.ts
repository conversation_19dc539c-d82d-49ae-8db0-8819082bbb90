import fs from 'fs/promises';
import path from 'path';
import { 
  DatabaseConnection, 
  DatabaseSchema, 
  SchemaExtractionProgress,
  EnrichedDatabaseSchema,
  DatabaseType 
} from './types';
import { MySQLSchemaExtractor } from './mysql-extractor';
import { MSSQLSchemaExtractor } from './mssql-extractor';

export class SchemaManager {
  private static instance: SchemaManager;
  private currentProgress: SchemaExtractionProgress | null = null;
  private progressCallbacks: ((progress: SchemaExtractionProgress) => void)[] = [];
  private currentConnection: DatabaseConnection | null = null;

  private constructor() {}

  static getInstance(): SchemaManager {
    if (!SchemaManager.instance) {
      SchemaManager.instance = new SchemaManager();
    }
    return SchemaManager.instance;
  }

  // إضافة مستمع لتحديثات التقدم
  onProgress(callback: (progress: SchemaExtractionProgress) => void): void {
    this.progressCallbacks.push(callback);
  }

  // إزالة مستمع التقدم
  removeProgressListener(callback: (progress: SchemaExtractionProgress) => void): void {
    const index = this.progressCallbacks.indexOf(callback);
    if (index > -1) {
      this.progressCallbacks.splice(index, 1);
    }
  }

  // تحديث التقدم وإشعار المستمعين
  private updateProgress(progress: Partial<SchemaExtractionProgress>): void {
    this.currentProgress = {
      ...this.currentProgress!,
      ...progress
    };
    
    this.progressCallbacks.forEach(callback => {
      callback(this.currentProgress!);
    });
  }

  // استخراج Schema من قاعدة البيانات
  async extractSchema(connection: DatabaseConnection): Promise<DatabaseSchema> {
    // تهيئة التقدم
    this.currentProgress = {
      currentStep: 'بدء الاتصال بقاعدة البيانات',
      totalSteps: 6,
      currentStepIndex: 0,
      tablesProcessed: 0,
      totalTables: 0,
      isComplete: false,
      logs: ['بدء عملية استخراج بنية قاعدة البيانات...']
    };

    this.updateProgress({});

    try {
      // إنشاء المستخرج المناسب
      const extractor = this.createExtractor(connection.type);
      
      // الاتصال بقاعدة البيانات
      this.updateProgress({
        currentStep: 'الاتصال بقاعدة البيانات...',
        currentStepIndex: 1,
        logs: [...this.currentProgress!.logs, `الاتصال بقاعدة بيانات ${connection.type}...`]
      });

      await extractor.connect(connection);

      // حفظ الاتصال الحالي لاستخدامه في executeQuery
      this.currentConnection = connection;

      // استخراج Schema
      this.updateProgress({
        currentStep: 'استخراج بنية قاعدة البيانات...',
        currentStepIndex: 2,
        logs: [...this.currentProgress!.logs, 'تم الاتصال بنجاح، بدء استخراج البنية...']
      });

      const schema = await extractor.extractSchema(connection.database);

      this.updateProgress({
        currentStep: 'معالجة الجداول...',
        currentStepIndex: 3,
        totalTables: schema.tables.length,
        logs: [...this.currentProgress!.logs, `تم العثور على ${schema.tables.length} جدول`]
      });

      // حفظ Schema
      this.updateProgress({
        currentStep: 'حفظ بنية قاعدة البيانات...',
        currentStepIndex: 4,
        logs: [...this.currentProgress!.logs, 'حفظ البنية في ملف schema.json...']
      });

      await this.saveSchema(schema);

      // قطع الاتصال
      this.updateProgress({
        currentStep: 'إنهاء الاتصال...',
        currentStepIndex: 5,
        logs: [...this.currentProgress!.logs, 'قطع الاتصال بقاعدة البيانات...']
      });

      await extractor.disconnect();

      // اكتمال العملية
      this.updateProgress({
        currentStep: 'تم الانتهاء بنجاح',
        currentStepIndex: 6,
        isComplete: true,
        logs: [...this.currentProgress!.logs, 'تم استخراج بنية قاعدة البيانات بنجاح!']
      });

      return schema;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      
      this.updateProgress({
        error: errorMessage,
        isComplete: true,
        logs: [...this.currentProgress!.logs, `خطأ: ${errorMessage}`]
      });

      throw error;
    }
  }

  // إنشاء المستخرج المناسب حسب نوع قاعدة البيانات
  private createExtractor(type: DatabaseType) {
    switch (type) {
      case 'mysql':
        return new MySQLSchemaExtractor();
      case 'mssql':
        return new MSSQLSchemaExtractor();
      default:
        throw new Error(`نوع قاعدة البيانات غير مدعوم: ${type}`);
    }
  }

  // حفظ Schema في ملف JSON
  async saveSchema(schema: DatabaseSchema): Promise<void> {
    try {
      const schemaDir = path.join(process.cwd(), 'data');
      
      // إنشاء مجلد data إذا لم يكن موجوداً
      try {
        await fs.access(schemaDir);
      } catch {
        await fs.mkdir(schemaDir, { recursive: true });
      }

      const schemaPath = path.join(schemaDir, 'schema.json');
      const schemaJson = JSON.stringify(schema, null, 2);
      
      await fs.writeFile(schemaPath, schemaJson, 'utf-8');
      
      console.log(`تم حفظ Schema في: ${schemaPath}`);
    } catch (error) {
      console.error('خطأ في حفظ Schema:', error);
      throw new Error(`فشل في حفظ Schema: ${error}`);
    }
  }

  // تحميل Schema من الملف
  async loadSchema(): Promise<DatabaseSchema | null> {
    try {
      const schemaPath = path.join(process.cwd(), 'data', 'schema.json');
      const schemaJson = await fs.readFile(schemaPath, 'utf-8');
      return JSON.parse(schemaJson) as DatabaseSchema;
    } catch (error) {
      console.log('لم يتم العثور على ملف Schema موجود');
      return null;
    }
  }

  // تحميل Schema المحسن (مع الأوصاف)
  async loadEnrichedSchema(): Promise<EnrichedDatabaseSchema | null> {
    try {
      const schemaPath = path.join(process.cwd(), 'data', 'enriched-schema.json');
      const schemaJson = await fs.readFile(schemaPath, 'utf-8');
      return JSON.parse(schemaJson) as EnrichedDatabaseSchema;
    } catch (error) {
      console.log('لم يتم العثور على ملف Schema المحسن');
      return null;
    }
  }

  // حفظ Schema المحسن
  async saveEnrichedSchema(schema: EnrichedDatabaseSchema): Promise<void> {
    try {
      const schemaDir = path.join(process.cwd(), 'data');
      
      // إنشاء مجلد data إذا لم يكن موجوداً
      try {
        await fs.access(schemaDir);
      } catch {
        await fs.mkdir(schemaDir, { recursive: true });
      }

      const schemaPath = path.join(schemaDir, 'enriched-schema.json');
      const schemaJson = JSON.stringify(schema, null, 2);
      
      await fs.writeFile(schemaPath, schemaJson, 'utf-8');
      
      console.log(`تم حفظ Schema المحسن في: ${schemaPath}`);
    } catch (error) {
      console.error('خطأ في حفظ Schema المحسن:', error);
      throw new Error(`فشل في حفظ Schema المحسن: ${error}`);
    }
  }

  // الحصول على التقدم الحالي
  getCurrentProgress(): SchemaExtractionProgress | null {
    return this.currentProgress;
  }

  // فحص ما إذا كان Schema موجود ومحدث
  async isSchemaUpToDate(connection: DatabaseConnection): Promise<boolean> {
    const schema = await this.loadSchema();
    if (!schema) return false;

    // فحص ما إذا كان Schema أقدم من 24 ساعة
    const extractedAt = new Date(schema.extractedAt);
    const now = new Date();
    const hoursDiff = (now.getTime() - extractedAt.getTime()) / (1000 * 60 * 60);

    return hoursDiff < 24;
  }

  // تنفيذ استعلام SQL وإرجاع النتائج
  async executeQuery(query: string): Promise<{ data: any[]; rowCount: number }> {
    // محاولة تحميل معلومات الاتصال إذا لم تكن موجودة
    if (!this.currentConnection) {
      this.currentConnection = await this.loadConnection();
      if (!this.currentConnection) {
        throw new Error('لا يوجد اتصال نشط بقاعدة البيانات');
      }
    }

    const extractor = this.createExtractor(this.currentConnection.type);

    try {
      // الاتصال بقاعدة البيانات
      await extractor.connect(this.currentConnection);

      // تنفيذ الاستعلام
      const results = await extractor.executeQuery(query);

      // قطع الاتصال
      await extractor.disconnect();

      return {
        data: results,
        rowCount: results.length
      };

    } catch (error) {
      console.error('خطأ في تنفيذ الاستعلام:', error);
      throw error;
    }
  }

  // تحديث معلومات الاتصال الحالي
  setCurrentConnection(connection: DatabaseConnection): void {
    this.currentConnection = connection;
  }

  // حفظ معلومات الاتصال
  async saveConnection(connection: DatabaseConnection): Promise<void> {
    try {
      const dataDir = path.join(process.cwd(), 'data');

      // إنشاء مجلد data إذا لم يكن موجوداً
      try {
        await fs.access(dataDir);
      } catch {
        await fs.mkdir(dataDir, { recursive: true });
      }

      // حفظ البيانات كما هي (بدون كلمة مرور إذا كانت فارغة)
      const safeConnection = {
        ...connection,
        password: connection.password || '' // حفظ كلمة المرور الفعلية أو فارغة
      };

      const connectionPath = path.join(dataDir, 'connection.json');
      const connectionJson = JSON.stringify(safeConnection, null, 2);

      await fs.writeFile(connectionPath, connectionJson, 'utf-8');

      console.log(`تم حفظ معلومات الاتصال في: ${connectionPath}`);
    } catch (error) {
      console.error('خطأ في حفظ معلومات الاتصال:', error);
    }
  }

  // تحميل معلومات الاتصال المحفوظة
  async loadConnection(): Promise<DatabaseConnection | null> {
    try {
      const connectionPath = path.join(process.cwd(), 'data', 'connection.json');
      const connectionJson = await fs.readFile(connectionPath, 'utf-8');
      return JSON.parse(connectionJson) as DatabaseConnection;
    } catch (error) {
      console.log('لم يتم العثور على ملف معلومات الاتصال');
      return null;
    }
  }
}
