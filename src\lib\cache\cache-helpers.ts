import { cacheManager, queryCache, descriptionCache } from './cache-manager';
import { TableDescription, AgentQueryResult } from '../database/types';

// مساعدات للكاش المتخصص

// كاش أوصاف الجداول
export class TableDescriptionCache {
  private static getKey(tableName: string, databaseName: string): string {
    return `table_desc_${databaseName}_${tableName}`;
  }

  static async get(tableName: string, databaseName: string): Promise<TableDescription | null> {
    const key = this.getKey(tableName, databaseName);
    return await descriptionCache.get<TableDescription>(key);
  }

  static async set(tableName: string, databaseName: string, description: TableDescription): Promise<void> {
    const key = this.getKey(tableName, databaseName);
    await descriptionCache.set(key, description, {
      metadata: { tableName, databaseName, type: 'table_description' }
    });
  }

  static async delete(tableName: string, databaseName: string): Promise<boolean> {
    const key = this.getKey(tableName, databaseName);
    return await descriptionCache.delete(key);
  }

  static async has(tableName: string, databaseName: string): Promise<boolean> {
    const key = this.getKey(tableName, databaseName);
    return await descriptionCache.has(key);
  }
}

// كاش نتائج الاستعلامات
export class QueryResultCache {
  private static getKey(query: string, databaseName: string): string {
    // تنظيف الاستعلام من المسافات الزائدة والأحرف الخاصة
    const cleanQuery = query.trim().toLowerCase().replace(/\s+/g, ' ');
    return `query_result_${databaseName}_${cleanQuery}`;
  }

  static async get(query: string, databaseName: string): Promise<AgentQueryResult | null> {
    const key = this.getKey(query, databaseName);
    return await queryCache.get<AgentQueryResult>(key);
  }

  static async set(query: string, databaseName: string, result: AgentQueryResult): Promise<void> {
    const key = this.getKey(query, databaseName);
    await queryCache.set(key, result, {
      ttl: 30 * 60 * 1000, // 30 دقيقة للاستعلامات
      metadata: { query, databaseName, type: 'query_result' }
    });
  }

  static async delete(query: string, databaseName: string): Promise<boolean> {
    const key = this.getKey(query, databaseName);
    return await queryCache.delete(key);
  }

  static async has(query: string, databaseName: string): Promise<boolean> {
    const key = this.getKey(query, databaseName);
    return await queryCache.has(key);
  }
}

// كاش Schema
export class SchemaCache {
  private static getKey(databaseName: string, type: 'basic' | 'enriched' = 'basic'): string {
    return `schema_${type}_${databaseName}`;
  }

  static async getBasicSchema(databaseName: string): Promise<any | null> {
    const key = this.getKey(databaseName, 'basic');
    return await cacheManager.get(key);
  }

  static async setBasicSchema(databaseName: string, schema: any): Promise<void> {
    const key = this.getKey(databaseName, 'basic');
    await cacheManager.set(key, schema, {
      ttl: 24 * 60 * 60 * 1000, // 24 ساعة
      metadata: { databaseName, type: 'basic_schema' }
    });
  }

  static async getEnrichedSchema(databaseName: string): Promise<any | null> {
    const key = this.getKey(databaseName, 'enriched');
    return await cacheManager.get(key);
  }

  static async setEnrichedSchema(databaseName: string, schema: any): Promise<void> {
    const key = this.getKey(databaseName, 'enriched');
    await cacheManager.set(key, schema, {
      ttl: 7 * 24 * 60 * 60 * 1000, // أسبوع
      metadata: { databaseName, type: 'enriched_schema' }
    });
  }

  static async deleteSchema(databaseName: string): Promise<void> {
    await cacheManager.delete(this.getKey(databaseName, 'basic'));
    await cacheManager.delete(this.getKey(databaseName, 'enriched'));
  }
}

// كاش Embeddings
export class EmbeddingCache {
  private static getKey(text: string): string {
    return `embedding_${text}`;
  }

  static async get(text: string): Promise<number[] | null> {
    const key = this.getKey(text);
    return await cacheManager.get<number[]>(key);
  }

  static async set(text: string, embedding: number[]): Promise<void> {
    const key = this.getKey(text);
    await cacheManager.set(key, embedding, {
      ttl: 30 * 24 * 60 * 60 * 1000, // 30 يوم
      metadata: { text: text.substring(0, 100), type: 'embedding' }
    });
  }

  static async delete(text: string): Promise<boolean> {
    const key = this.getKey(text);
    return await cacheManager.delete(key);
  }

  static async has(text: string): Promise<boolean> {
    const key = this.getKey(text);
    return await cacheManager.has(key);
  }
}

// مساعدات عامة للكاش
export class CacheHelpers {
  // مسح كاش قاعدة بيانات معينة
  static async clearDatabaseCache(databaseName: string): Promise<void> {
    await SchemaCache.deleteSchema(databaseName);
    // يمكن إضافة المزيد من عمليات المسح هنا
  }

  // الحصول على إحصائيات شاملة للكاش
  static getCacheStats(): {
    general: any;
    queries: any;
    descriptions: any;
  } {
    return {
      general: cacheManager.getStats(),
      queries: queryCache.getStats(),
      descriptions: descriptionCache.getStats()
    };
  }

  // مسح جميع أنواع الكاش
  static async clearAllCaches(): Promise<void> {
    await Promise.all([
      cacheManager.clear(),
      queryCache.clear(),
      descriptionCache.clear()
    ]);
  }

  // تنظيف الكاش المنتهي الصلاحية
  static async cleanupExpiredCache(): Promise<void> {
    // هذه العملية تتم تلقائياً في CacheManager
    // لكن يمكن إضافة منطق إضافي هنا إذا لزم الأمر
  }
}

// دالة مساعدة لتنفيذ عملية مع كاش
export async function withCache<T>(
  key: string,
  fetchFunction: () => Promise<T>,
  options?: {
    ttl?: number;
    forceRefresh?: boolean;
    cacheType?: 'general' | 'query' | 'description';
  }
): Promise<T> {
  const cacheType = options?.cacheType || 'general';
  const cache = cacheType === 'query' ? queryCache : 
                cacheType === 'description' ? descriptionCache : 
                cacheManager;

  // فحص الكاش أولاً (إلا إذا كان مطلوب تحديث قسري)
  if (!options?.forceRefresh) {
    const cached = await cache.get<T>(key);
    if (cached !== null) {
      return cached;
    }
  }

  // تنفيذ العملية وحفظ النتيجة في الكاش
  const result = await fetchFunction();
  await cache.set(key, result, { ttl: options?.ttl });
  
  return result;
}
