'use client';

import { useState } from 'react';
import { Activity, AlertTriangle, CheckCircle, Clock, RefreshCw } from 'lucide-react';

interface APIStatus {
  isHealthy: boolean;
  latency?: number;
  error?: string;
  lastCheck?: string;
}

export function APIStatus() {
  const [status, setStatus] = useState<APIStatus | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const checkAPIStatus = async () => {
    setIsChecking(true);
    
    try {
      const startTime = Date.now();
      const response = await fetch('/api/diagnostics/network', {
        method: 'POST' // استخدام POST للاختبار السريع
      });
      const latency = Date.now() - startTime;
      
      const result = await response.json();
      
      if (result.status === 'success') {
        setStatus({
          isHealthy: result.quickTest.internetConnection,
          latency,
          lastCheck: new Date().toISOString()
        });
      } else {
        setStatus({
          isHealthy: false,
          error: result.error,
          latency,
          lastCheck: new Date().toISOString()
        });
      }
    } catch (error) {
      setStatus({
        isHealthy: false,
        error: error instanceof Error ? error.message : 'خطأ في الاتصال',
        lastCheck: new Date().toISOString()
      });
    } finally {
      setIsChecking(false);
    }
  };

  const getStatusColor = (isHealthy: boolean) => {
    return isHealthy ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (isHealthy: boolean) => {
    return isHealthy ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <AlertTriangle className="w-5 h-5 text-red-500" />
    );
  };

  const getLatencyColor = (latency?: number) => {
    if (!latency) return 'text-gray-500';
    if (latency < 1000) return 'text-green-600';
    if (latency < 3000) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getLatencyLabel = (latency?: number) => {
    if (!latency) return 'غير معروف';
    if (latency < 1000) return 'سريع';
    if (latency < 3000) return 'متوسط';
    return 'بطيء';
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-blue-600" />
          <h4 className="font-medium text-gray-900">حالة API</h4>
        </div>
        
        <button
          onClick={checkAPIStatus}
          disabled={isChecking}
          className="flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <RefreshCw className={`w-4 h-4 ${isChecking ? 'animate-spin' : ''}`} />
          {isChecking ? 'فحص...' : 'فحص'}
        </button>
      </div>

      {status ? (
        <div className="space-y-3">
          {/* حالة الاتصال */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon(status.isHealthy)}
              <span className="text-sm font-medium">الحالة</span>
            </div>
            <span className={`text-sm font-medium ${getStatusColor(status.isHealthy)}`}>
              {status.isHealthy ? 'متصل' : 'غير متصل'}
            </span>
          </div>

          {/* زمن الاستجابة */}
          {status.latency && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-gray-400" />
                <span className="text-sm font-medium">زمن الاستجابة</span>
              </div>
              <span className={`text-sm font-medium ${getLatencyColor(status.latency)}`}>
                {status.latency}ms ({getLatencyLabel(status.latency)})
              </span>
            </div>
          )}

          {/* رسالة الخطأ */}
          {status.error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-700">{status.error}</p>
            </div>
          )}

          {/* آخر فحص */}
          {status.lastCheck && (
            <div className="text-xs text-gray-500 text-center">
              آخر فحص: {new Date(status.lastCheck).toLocaleTimeString('ar-SA')}
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-4">
          <Activity className="w-8 h-8 mx-auto mb-2 text-gray-300" />
          <p className="text-sm text-gray-500">اضغط "فحص" لمعرفة حالة API</p>
        </div>
      )}
    </div>
  );
}

// مكون مبسط لعرض حالة سريعة
export function QuickAPIStatus() {
  const [status, setStatus] = useState<'unknown' | 'checking' | 'healthy' | 'unhealthy'>('unknown');

  const checkQuickStatus = async () => {
    setStatus('checking');
    
    try {
      const response = await fetch('/api/diagnostics/network', {
        method: 'POST',
        signal: AbortSignal.timeout(5000)
      });
      
      const result = await response.json();
      setStatus(result.status === 'success' && result.quickTest.internetConnection ? 'healthy' : 'unhealthy');
    } catch {
      setStatus('unhealthy');
    }
  };

  const getStatusDisplay = () => {
    switch (status) {
      case 'checking':
        return {
          icon: <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />,
          text: 'فحص...',
          color: 'text-blue-600'
        };
      case 'healthy':
        return {
          icon: <CheckCircle className="w-4 h-4 text-green-500" />,
          text: 'متصل',
          color: 'text-green-600'
        };
      case 'unhealthy':
        return {
          icon: <AlertTriangle className="w-4 h-4 text-red-500" />,
          text: 'غير متصل',
          color: 'text-red-600'
        };
      default:
        return {
          icon: <Activity className="w-4 h-4 text-gray-400" />,
          text: 'غير معروف',
          color: 'text-gray-500'
        };
    }
  };

  const statusDisplay = getStatusDisplay();

  return (
    <button
      onClick={checkQuickStatus}
      disabled={status === 'checking'}
      className="flex items-center gap-2 px-3 py-1.5 text-sm border border-gray-200 rounded-md hover:bg-gray-50 disabled:cursor-not-allowed transition-colors"
    >
      {statusDisplay.icon}
      <span className={statusDisplay.color}>{statusDisplay.text}</span>
    </button>
  );
}
