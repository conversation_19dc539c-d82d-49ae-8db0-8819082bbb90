// نظام تعريب أسماء الأعمدة والجداول

export interface ColumnTranslation {
  [key: string]: string;
}

// قاموس ترجمة أسماء الأعمدة الشائعة
export const COLUMN_TRANSLATIONS: ColumnTranslation = {
  // أعمدة العملاء
  'customer_id': 'رقم العميل',
  'customer_name': 'اسم العميل',
  'phone': 'رقم الهاتف',
  'email': 'البريد الإلكتروني',
  'address': 'العنوان',
  
  // أعمدة المنتجات
  'item_id': 'رقم المنتج',
  'item_name': 'اسم المنتج',
  'item_code': 'كود المنتج',
  'unit_price': 'سعر الوحدة',
  'unit_cost': 'تكلفة الوحدة',
  'stock_quantity': 'الكمية المتوفرة',
  'category': 'الفئة',
  'description': 'الوصف',
  
  // أعمدة الفواتير
  'invoice_id': 'رقم الفاتورة',
  'invoice_date': 'تاريخ الفاتورة',
  'total_amount': 'المبلغ الإجمالي',
  'invoice_detail_id': 'رقم تفصيل الفاتورة',
  'quantity': 'الكمية',
  'subtotal': 'المجموع الفرعي',
  
  // أعمدة المشتريات
  'purchase_id': 'رقم المشترى',
  'purchase_date': 'تاريخ الشراء',
  'supplier_id': 'رقم المورد',
  'supplier_name': 'اسم المورد',
  
  // أعمدة حركة المخزون
  'movement_id': 'رقم الحركة',
  'movement_type': 'نوع الحركة',
  'movement_date': 'تاريخ الحركة',
  'remarks': 'ملاحظات',
  
  // أعمدة إحصائية شائعة
  'total_purchases': 'إجمالي المشتريات',
  'total_sold': 'إجمالي المبيعات',
  'total_revenue': 'إجمالي الإيرادات',
  'total_invoices': 'عدد الفواتير',
  'average_sale': 'متوسط البيع',
  'count': 'العدد',
  'sum': 'المجموع',
  'avg': 'المتوسط',
  'min': 'الحد الأدنى',
  'max': 'الحد الأقصى',
  
  // أعمدة التواريخ
  'created_at': 'تاريخ الإنشاء',
  'updated_at': 'تاريخ التحديث',
  'deleted_at': 'تاريخ الحذف',
  
  // أعمدة أخرى شائعة
  'status': 'الحالة',
  'notes': 'ملاحظات',
  'discount': 'الخصم',
  'tax': 'الضريبة',
  'profit': 'الربح',
  'loss': 'الخسارة'
};

// قاموس ترجمة أسماء الجداول
export const TABLE_TRANSLATIONS: ColumnTranslation = {
  'customers': 'العملاء',
  'items': 'المنتجات',
  'invoices': 'الفواتير',
  'invoice_details': 'تفاصيل الفواتير',
  'purchases': 'المشتريات',
  'purchase_details': 'تفاصيل المشتريات',
  'suppliers': 'الموردين',
  'stock_movements': 'حركة المخزون',
  'categories': 'الفئات'
};

// دالة لترجمة اسم عمود واحد
export function translateColumn(columnName: string): string {
  // إزالة المسافات والأحرف الخاصة
  const cleanName = columnName.toLowerCase().trim();
  
  // البحث في القاموس
  if (COLUMN_TRANSLATIONS[cleanName]) {
    return COLUMN_TRANSLATIONS[cleanName];
  }
  
  // محاولة تطبيق قواعد ترجمة تلقائية
  return applyAutoTranslation(columnName);
}

// دالة لترجمة اسم جدول
export function translateTable(tableName: string): string {
  const cleanName = tableName.toLowerCase().trim();
  return TABLE_TRANSLATIONS[cleanName] || tableName;
}

// دالة لترجمة مجموعة من أسماء الأعمدة
export function translateColumns(columns: string[]): string[] {
  return columns.map(col => translateColumn(col));
}

// دالة لتطبيق قواعد ترجمة تلقائية
function applyAutoTranslation(columnName: string): string {
  const name = columnName.toLowerCase();
  
  // قواعد الترجمة التلقائية
  if (name.includes('_id') || name.includes('id')) {
    return `رقم ${name.replace(/_id|id/g, '').replace(/_/g, ' ')}`;
  }
  
  if (name.includes('_name') || name.includes('name')) {
    return `اسم ${name.replace(/_name|name/g, '').replace(/_/g, ' ')}`;
  }
  
  if (name.includes('_date') || name.includes('date')) {
    return `تاريخ ${name.replace(/_date|date/g, '').replace(/_/g, ' ')}`;
  }
  
  if (name.includes('total_')) {
    return `إجمالي ${name.replace(/total_/g, '').replace(/_/g, ' ')}`;
  }
  
  if (name.includes('_count') || name.includes('count')) {
    return `عدد ${name.replace(/_count|count/g, '').replace(/_/g, ' ')}`;
  }
  
  // إذا لم تنطبق أي قاعدة، أرجع الاسم الأصلي مع تنسيق أفضل
  return columnName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

// دالة لترجمة كائن البيانات مع الحفاظ على القيم
export function translateDataObject(data: any): any {
  if (!data || typeof data !== 'object') return data;
  
  const translatedData: any = {};
  
  for (const [key, value] of Object.entries(data)) {
    const translatedKey = translateColumn(key);
    translatedData[translatedKey] = value;
  }
  
  return translatedData;
}

// دالة لترجمة مصفوفة من كائنات البيانات
export function translateDataArray(dataArray: any[]): any[] {
  if (!Array.isArray(dataArray) || dataArray.length === 0) return dataArray;
  
  return dataArray.map(item => translateDataObject(item));
}

// دالة للحصول على الترجمة العكسية (من العربية للإنجليزية)
export function getReverseTranslation(arabicName: string): string | null {
  for (const [english, arabic] of Object.entries(COLUMN_TRANSLATIONS)) {
    if (arabic === arabicName) {
      return english;
    }
  }
  return null;
}

// دالة لإضافة ترجمات مخصصة
export function addCustomTranslations(customTranslations: ColumnTranslation): void {
  Object.assign(COLUMN_TRANSLATIONS, customTranslations);
}

// دالة للتحقق من وجود ترجمة لعمود معين
export function hasTranslation(columnName: string): boolean {
  const cleanName = columnName.toLowerCase().trim();
  return COLUMN_TRANSLATIONS.hasOwnProperty(cleanName);
}

// دالة لتنسيق القيم حسب نوع العمود
export function formatColumnValue(columnName: string, value: any): string {
  if (value === null || value === undefined) return '-';
  
  const name = columnName.toLowerCase();
  
  // تنسيق التواريخ
  if (name.includes('date') || name.includes('تاريخ')) {
    if (value instanceof Date) {
      return value.toLocaleDateString('ar-SA');
    }
    if (typeof value === 'string' && !isNaN(Date.parse(value))) {
      return new Date(value).toLocaleDateString('ar-SA');
    }
  }
  
  // تنسيق الأرقام
  if (typeof value === 'number') {
    // أسعار ومبالغ
    if (name.includes('price') || name.includes('amount') || name.includes('cost') || 
        name.includes('سعر') || name.includes('مبلغ') || name.includes('تكلفة')) {
      return `${value.toLocaleString('ar-SA')} ر.س`;
    }
    // كميات
    if (name.includes('quantity') || name.includes('stock') || name.includes('كمية')) {
      return value.toLocaleString('ar-SA');
    }
    // أرقام عامة
    return value.toLocaleString('ar-SA');
  }
  
  // قيم منطقية
  if (typeof value === 'boolean') {
    return value ? 'نعم' : 'لا';
  }
  
  return String(value);
}
